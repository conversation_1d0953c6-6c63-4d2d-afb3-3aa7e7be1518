package com.tcl.ai.note.helpwriting.ui

import android.content.res.Configuration
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.tcl.ai.note.helpwriting.ui.compose.HelpMeWriteScreen
import com.tcl.ai.note.helpwriting.viewmodel.HelpWritingViewModel
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.Logger
import dagger.hilt.android.AndroidEntryPoint

/**
 * 帮写的Activity
 */
@AndroidEntryPoint
class HelpMeWriteActivity : AppCompatActivity() {

    lateinit var reWriteViewModel: HelpWritingViewModel
    companion object {
        private const val TAG = "HelpMeWriteActivity"
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        reWriteViewModel = ViewModelProvider(this)[HelpWritingViewModel::class.java]
        intent?.getLongExtra("noteId", 0)?.let {
            Logger.d(TAG,"noteId =  $it")
            reWriteViewModel.initNote(it)
        }
        setContent {
            NoteTclTheme {
                HelpMeWriteScreen(
                    onFinish = {
                        finish()
                    }
                )
            }
        }
    }
    /**
     * TCL AI方案 切换暗黑模式 重新设置enableEdgeToEdge()
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enableEdgeToEdge()
    }
}