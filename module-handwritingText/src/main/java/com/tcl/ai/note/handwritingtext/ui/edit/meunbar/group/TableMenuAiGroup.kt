package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.group

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.vm.MenuBarUiState
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.VerticalLine

/**
 * 顶部菜单AI工具分组
 */
@Composable
internal fun TableMenuAiGroup(
    menuBarState: MenuBarUiState,
){
    val isBeautifyActive = menuBarState.isBeautifyActive
    val isPanOnly = menuBarState.isPanOnly
    val dimens = getGlobalDimens()
    val menuColorItems = remember {
        val menuBars = listOf(
            MenuBarItem.AI(customContent = { modifier, item ->
                HoverProofIconButton(
                    modifier = modifier
                        .size(dimens.btnSize),
                    rippleSize = dimens.btnSize,
                    onClick = {}
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(R.drawable.ic_ai_icon),
                            contentDescription = stringResource(R.string.edit_bottom_menu_ai_assistant),
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }),

            // 其他工具
            MenuBarItem.Beautify.apply {
                isChecked = isBeautifyActive
                onClick = {  }
            },
            MenuBarItem.PanOnly.apply {
                isChecked = isPanOnly
                onClick = {}
            }
        )
        menuBars
    }
    Row(
        modifier = Modifier.height(dimens.menuBarHeight),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ){
        VerticalLine()
        Spacer(Modifier.width(16.dp))
        menuColorItems.forEachIndexed { index, item ->
            val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
                val position = layoutCoordinates.localToWindow(Offset.Zero)
                item.position =position
            }
            if (item.customContent != null) {
                item.customContent?.invoke(modifierPos,item)
            }else{
                DelayedBackgroundIconButton(
                    btnSize = item.btnSize,
                    painter = painterResource(item.iconRes!!),
                    isChecked = item.isChecked,
                    enabled = item.isEnabled,
                    contentDescription = stringResource(item.descriptionRes),
                    onClick = { item.onClick.invoke(item) }
                )
            }
            Spacer(Modifier.width(16.dp))

        }
    }


}
