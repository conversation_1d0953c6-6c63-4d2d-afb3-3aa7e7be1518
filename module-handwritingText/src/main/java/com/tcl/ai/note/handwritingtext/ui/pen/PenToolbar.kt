package com.tcl.ai.note.handwritingtext.ui.pen

import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.sunia.penengine.sdk.operate.touch.PenProp
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.handwritingtext.bean.ColorSource
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.bean.progressToPenSize
import com.tcl.ai.note.handwritingtext.bean.toPenType
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CustomRadioButton
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel
import com.tcl.ai.note.handwritingtext.ui.swatches.getColorDescription
import com.tcl.ai.note.handwritingtext.ui.swatches.toHslArray
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.borderCircle
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.drawableRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.clickableNoRipple

/**
 * 笔刷工具
 */
@Composable
fun PenToolbar(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    modifier: Modifier = Modifier,
    viewModel: PenToolbarViewModel = hiltViewModel(),
    onSwitchBrush:(PenProp) -> Unit,
    onChangeBrushSize:(Float) -> Unit,
    onChangePenColor:(penColor:PenColor) -> Unit,
    onOpenColorPicker: () -> Unit,
) {
    val pens = viewModel.penStyles
    val selectedIndex = viewModel.penSelectedIndex

    val alternativeColors by viewModel.alternativeColors.collectAsState()
    Column(
        modifier =modifier
            .padding(horizontal = 12.dp),
    ) {
        Spacer(Modifier.height(24.dp))
        Text(
            modifier = Modifier.fillMaxWidth().padding(horizontal = 12.dp),
            fontSize = 20.sp,
            lineHeight = 24.sp,
            fontFamily = FontFamily.Default,
            fontWeight = FontWeight.Medium,
            color = R.color.popup_title.colorRes(),
            text = com.tcl.ai.note.base.R.string.pen_popup_brush_tool.stringRes(),
        )
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            verticalAlignment = Alignment.Bottom,
        ) {
            Spacer(Modifier.width(12.dp))
            pens.forEachIndexed { i, pen ->
                PenItem(
                    modifier = Modifier
                        .clipToBounds(),
                    isDarkTheme = isDarkTheme,
                    penStyle = pen,
                    selected = i == selectedIndex,
                    onClick = {
                        viewModel.selectPen(i)
                        viewModel.selectedPen.apply {
                            val penProp = PenProp(toPenType().value,color.toArgb(),width,alpha)
                            onSwitchBrush(penProp)

                        }

                    },
                )
                Spacer(Modifier.width(20.dp))

            }

        }
        BrushSizeSlider(
            modifier= Modifier.height(48.dp),
            lastProgress = viewModel.selectedPen.curProgress,
            onProgressChanged = { progress ->
            val width = viewModel.selectedPen.progressToPenSize(progress)
            viewModel.updateSelectedPen(width = width, curProgress = progress)
            onChangeBrushSize(width)
        })
        AlternativeColorsBar(
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
                .padding(horizontal = 12.dp),
            isDarkTheme = isDarkTheme,
            penColors = isDarkTheme.judge(
                alternativeColors.map { penColor ->
                    penColor.copy(color = penColor.color.inverseColor())
                },
                alternativeColors
            ),
            selColor = PenColor(
                isDarkTheme.judge(
                    viewModel.selectedPen.color.inverseColor(),
                    viewModel.selectedPen.color
                )
                ,
                viewModel.selectedPen.alpha),
            onColorClick = { penColor ->
                val mPenColor = penColor.copy(
                    color = isDarkTheme.judge(
                        penColor.color.inverseColor(),
                        penColor.color
                    ),
                    alpha = penColor.alpha
                )
                viewModel.updateSelectedPen(
                    color = mPenColor.color,
                    alpha = mPenColor.alpha)
                onChangePenColor(mPenColor)
            },
            onOpenColorPicker = onOpenColorPicker
        )
        Spacer(Modifier.height(16.dp))
    }


}
@Composable
internal fun AlternativeColorsBar(
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    selColor: PenColor,
    penColors: List<PenColor>,
    onColorClick: (PenColor) -> Unit,
    onOpenColorPicker: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var isHas by remember(selColor.color) { mutableStateOf(false) }


    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        penColors.forEach { penColor ->
            val hsl = penColor.color.toHslArray()
            val desc = getColorDescription(context,hsl[0], hsl[1], hsl[2])
            if(penColor == selColor){
                isHas = true
            }
            Box(
                modifier = Modifier.size(24.dp).semantics {
                    this.contentDescription =context.getString(com.tcl.ai.note.base.R.string.color_swatch_collection).plus(desc)
                    this.role = Role.Button
                },
                contentAlignment = Alignment.Center
            ){
                CustomRadioButton(
                    modifier = Modifier.invisibleSemantics(),
                    isDarkTheme = isDarkTheme,
                    selected = (penColor == selColor),
                    color = penColor.color,
                    onClick = {
                        onColorClick(penColor)
                    },
                    outerSize =20.dp
                )
            }
        }
        Box(
            modifier = Modifier.size(24.dp).clearAndSetSemantics {
                this.contentDescription =context.getString(com.tcl.ai.note.base.R.string.more_colors)
                this.role = Role.Button
            },
            contentAlignment = Alignment.Center
        ){
            Box(
                modifier = Modifier.size(20.dp)
                    .clickableNoRipple {
                        onOpenColorPicker()
                    }
                    .then(isHas.judge(
                        Modifier,
                        Modifier.border(
                            width = 2.dp,
                            color =R.color.radio_sel.colorRes(),
                            shape = CircleShape
                        )
                    )),
                contentAlignment = Alignment.Center,
            ){
                Image(
                    painter = R.drawable.ic_colour_disc.drawableRes(),
                    contentDescription = "",
                    modifier = Modifier.size(isHas.judge(20.dp,10.dp))
                )
            }
        }



    }
}


