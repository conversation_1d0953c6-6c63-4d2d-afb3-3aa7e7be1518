package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.R as HR
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.drawableRes
import com.tcl.ai.note.widget.LongPressIconButton
import kotlinx.coroutines.delay
import kotlin.math.roundToInt

@Composable
fun EraserSizeSlider(
    eraserSize: Int,
    onSizeChanged: (Int) -> Unit,
    onDragStateChanged: (Boolean) -> Unit
) {
    val density = LocalDensity.current
    val context = LocalContext.current
    var sliderWidth by remember { mutableIntStateOf(0) }
    var currentSize by remember { mutableIntStateOf(eraserSize) }

    var isLongPressingLess by remember { mutableStateOf(false) }
    var isLongPressingMore by remember { mutableStateOf(false) }

    LaunchedEffect(eraserSize) {
        if (!isLongPressingLess && !isLongPressingMore) {
            currentSize = eraserSize
        }
    }

    LaunchedEffect(sliderWidth, eraserSize) {
        if (sliderWidth > 0 && !isLongPressingLess && !isLongPressingMore) {
            currentSize = eraserSize
        }
    }

    LaunchedEffect(isLongPressingLess) {
        while (isLongPressingLess) {
            currentSize = (currentSize - 1).coerceAtLeast(10)
            onSizeChanged(currentSize)
            delay(100)
        }
    }

    LaunchedEffect(isLongPressingMore) {
        while (isLongPressingMore) {
            currentSize = (currentSize + 1).coerceAtMost(100)
            onSizeChanged(currentSize)
            delay(100)
        }
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box {
            LongPressIconButton(
                onLongPress = { isLongPressing ->
                    isLongPressingLess = isLongPressing
                    onDragStateChanged(isLongPressing) // 触发蒙层显示/隐藏
                },
                onClick = {
                    currentSize = (currentSize - 1).coerceAtLeast(10)
                    onSizeChanged(currentSize)
                },
                modifier = Modifier.size(32.dp),
            ) {
                Icon(
                    painter = HR.drawable.ic_value_decrease.drawableRes(),
                    contentDescription = stringResource(R.string.eraser_value_decrease)
                )
            }
        }

        Spacer(Modifier.width(8.dp))

        Box(
            modifier = Modifier
                .width(160.dp)
                .semantics {
                    contentDescription= String.format(context.getString(R.string.eraser_slider_status),"${currentSize}")
                }
                .onSizeChanged { size ->
                    sliderWidth = size.width
                },
        ) {
            RoundedBarWithDots(modifier = Modifier.align(Alignment.Center))
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .pointerInput(sliderWidth) {
                        awaitEachGesture {
                            val firstEvent = awaitPointerEvent()
                            val finger = firstEvent.changes.firstOrNull() ?: return@awaitEachGesture
                            if (!finger.pressed) return@awaitEachGesture

                            onDragStateChanged(true)

                            try {
                                while (true) {
                                    val event = awaitPointerEvent()
                                    val currentFinger = event.changes.firstOrNull() ?: break
                                    if (!currentFinger.pressed) break

                                    val pos = currentFinger.position
                                    // 使用实际滑块宽度进行计算
                                    val trackWidthPx = sliderWidth
                                    val indicatorHalfWidthPx = with(density) { 9.dp.toPx() }.roundToInt()
                                    val leftPaddingPx = 0
                                    val rightPaddingPx = with(density) { 1.5.dp.toPx() }.roundToInt()

                                    val minCenterX = leftPaddingPx + indicatorHalfWidthPx
                                    val maxCenterX = trackWidthPx - rightPaddingPx - indicatorHalfWidthPx
                                    val moveRange = maxCenterX - minCenterX

                                    if (moveRange > 0) {
                                        val adjustedX = (pos.x - minCenterX).coerceIn(0f, moveRange.toFloat())
                                        val ratio = (adjustedX / moveRange.toFloat()).coerceIn(0f, 1f)
                                        currentSize = (ratio * 90 + 10).roundToInt()
                                        onSizeChanged(currentSize)
                                    }
                                }
                            } finally {
                                onDragStateChanged(false)
                            }
                        }
                    }
            )

            // 重新计算指示器位置，微调左右padding平衡视觉效果
            val trackWidthPx = sliderWidth
            val indicatorHalfWidthPx = 7.dp2px
            val leftPaddingPx = 2.dp2px // 左边padding为 2 dp
            val rightPaddingPx =  3.dp2px // 右边保持 3 dp

            // 指示器中心可移动的范围：左右padding不同
            val minCenterX = leftPaddingPx + indicatorHalfWidthPx
            val maxCenterX = trackWidthPx - rightPaddingPx - indicatorHalfWidthPx
            val moveRange = maxCenterX - minCenterX

            val progress = (currentSize - 10) / 90f
            val handleCenterX = if (moveRange > 0) {
                minCenterX + (progress * moveRange).roundToInt()
            } else {
                minCenterX
            }
            // 从中心位置减去指示器半径，得到左上角位置，并保持原有的1像素右偏移
            val handleOffsetX = handleCenterX - indicatorHalfWidthPx + 1

            Box(
                Modifier
                    .offset {
                        IntOffset(
                            x = handleOffsetX, // 左上角位置
                            y = with(density) { 3.dp.toPx() }.roundToInt() // marginTop: 1dp
                        )
                    }
                    .size(14.dp)
                    .shadow(
                        elevation = 4.dp,
                        shape = RoundedCornerShape(9.dp),
                        ambientColor = Color(0x1A000000),
                        spotColor = Color(0x1A000000)
                    )
                    .clip(RoundedCornerShape(7.dp))
                    .background(Color.White),
                contentAlignment = Alignment.Center
            ) {
                /*Text(
                    text = "$currentSize",
                    fontSize = 8.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333),
                    fontFamily = FontFamily.Default,
                    textAlign = TextAlign.Center
                )*/
            }
        }

        Spacer(Modifier.width(8.dp))

        Box {
            LongPressIconButton(
                onLongPress = { isLongPressing ->
                    isLongPressingMore = isLongPressing
                    onDragStateChanged(isLongPressing) // 触发蒙层显示/隐藏
                },
                onClick = {
                    currentSize = (currentSize + 1).coerceAtMost(100)
                    onSizeChanged(currentSize)
                },
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    painter = HR.drawable.ic_value_increase.drawableRes(),
                    contentDescription = stringResource(R.string.eraser_value_increase)
                )
            }
        }

        Spacer(Modifier.width(8.dp))

        // 右侧大小显示框 - 36dp x 24dp
        Box(
            modifier = Modifier
                .width(36.dp)
                .height(24.dp)
                .border(
                    width = 1.dp,
                    color = com.tcl.ai.note.handwritingtext.R.color.slider_indicator_border.colorRes(),
                    shape = RoundedCornerShape(4.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "$currentSize",
                fontSize = 12.sp,
                fontWeight = FontWeight.W500,
                color = com.tcl.ai.note.handwritingtext.R.color.slider_indicator.colorRes(),
                fontFamily = FontFamily.Default,
                textAlign = TextAlign.Center,
                modifier = Modifier.offset(y = (0).dp) // 微调文字位置，让数字居中
            )
        }
    }
}



// 滑块轨道背景，5个点
@Composable
private fun RoundedBarWithDots(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxWidth() // 使用fillMaxWidth而不是固定宽度
            .height(20.dp)
            .clip(RoundedCornerShape(20.dp))
            .background(TclTheme.colorScheme.itemSelectedBackground),
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier.fillMaxWidth().padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            repeat(5) {
                Box(
                    modifier = Modifier
                        .size(4.dp)
                        .clip(RoundedCornerShape(2.dp))
                        .background(color = TclTheme.colorScheme.lightGray)
                )
            }
        }
    }
}
