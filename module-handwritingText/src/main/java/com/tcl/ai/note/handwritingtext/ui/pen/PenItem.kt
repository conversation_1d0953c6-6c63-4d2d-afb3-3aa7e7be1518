package com.tcl.ai.note.handwritingtext.ui.pen

import android.util.Log
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.sunia.penengine.sdk.operate.touch.PenType
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.handwritingtext.bean.PenStyle
import com.tcl.ai.note.handwritingtext.bean.getDescription
import com.tcl.ai.note.handwritingtext.bean.toPenType
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.utils.drawableRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.utils.toHex
import com.tcl.ai.note.widget.clickableNoRipple

@Composable
fun PenItem(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    modifier: Modifier = Modifier,
    penStyle: PenStyle,
    selected: Boolean,
    onClick: () -> Unit,
    penHeight: Dp = 96.dp,
    penWidth: Dp = 50.dp,
) {
    val yOffset = if (selected) penHeight * 0.054f else penHeight * 0.288f
    val animOffset by animateDpAsState(targetValue = yOffset, label = "yOffset")

    val context = LocalContext.current

    Box(
        modifier = modifier
            .height(penHeight)
            .width(penWidth)
            .clipToBounds()
            .semantics {
                this.role = Role.Button
                this.contentDescription =context.getString(penStyle.getDescription())
            }
            .clickableNoRipple { onClick()},
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    translationY = animOffset.toPx()
                }
        ) {
            Image(
                modifier = Modifier.padding(top = 10.dp).align(Alignment.Center),
                painter = R.drawable.light_pen_shadow.drawableRes(),
                contentDescription = null,
            )
            Image(
                modifier = Modifier
                    .width(30.dp)
                    .align(Alignment.TopEnd)
                    .aspectRatio(30f/105f),
                painter = painterResource(id = penStyle.nibResId),
                contentDescription = null,
                colorFilter =isDarkTheme.judge(
                    ColorFilter.tint(penStyle.color.inverseColor()),
                    ColorFilter.tint((penStyle.color == Color.White).judge(Color.Transparent, penStyle.color))
                ),
                contentScale = ContentScale.FillHeight
            )
            Image(
                painter = painterResource(id = penStyle.hullResId),
                modifier = Modifier
                    .width(30.dp)
                    .align(Alignment.TopEnd)
                    .aspectRatio(30f/105f),
                contentDescription = null,
                contentScale = ContentScale.FillHeight
            )

        }
    }
}


