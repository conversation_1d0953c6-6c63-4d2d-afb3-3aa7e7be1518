package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.group

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.menHullIcon
import com.tcl.ai.note.handwritingtext.bean.menNibIcon
import com.tcl.ai.note.handwritingtext.ui.pen.PenTurnColorButton
import com.tcl.ai.note.handwritingtext.state.MenuEvent
import com.tcl.ai.note.handwritingtext.state.MenuPopupState
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.handler.MenuCallbacks
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.handler.TabletMenuEventHandler
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup.TabletMenuPopupManager
import com.tcl.ai.note.handwritingtext.vm.MenuBarUiState
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.ColorGroupViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.VerticalLine

/**
 * 菜单栏
 */
@Deprecated("因为存在bug，暂不投入时间，只有参考意义")
@Composable
fun TabletMenuToolGroup(
    penToolbarViewModel: PenToolbarViewModel = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    colorGroupViewModel: ColorGroupViewModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
    eraserViewModel: EraserViewModel = hiltViewModel(),
    menuBarState: MenuBarUiState,
    setPopupComposable: ((@Composable () -> Unit)?) -> Unit,
    onOpenEraser: () -> Unit,
    onOpenBrush: () -> Unit,
    onOpenKeyboard: () -> Unit
) {
    val selectedPen = penToolbarViewModel.selectedPen
    val dimens = getGlobalDimens()
    val focusManager = LocalFocusManager.current

    var popupState by remember { mutableStateOf<MenuPopupState>(MenuPopupState.None) }
    var lastDismissTime by remember { mutableLongStateOf(0L) }
    
    // 监听这些 lambda 的对象引用变化
    val eventHandler = remember(
        onOpenEraser,
        onOpenBrush,
        onOpenKeyboard
    ) { // 上面3个 lambda 中任何一个变化时才会创建
        TabletMenuEventHandler(
            penToolbarViewModel = penToolbarViewModel,
            suniaDrawViewModel = suniaDrawViewModel,
            colorGroupViewModel = colorGroupViewModel,
            menuBarViewModel = menuBarViewModel,
            eraserViewModel = eraserViewModel,
            focusManager = focusManager,
            callbacks = MenuCallbacks(
                onOpenEraser = onOpenEraser,
                onOpenBrush = onOpenBrush,
                onOpenKeyboard = onOpenKeyboard
            )
        )
    }

    // 初始化默认笔刷设置
    LaunchedEffect(penToolbarViewModel.initCount) {
        eventHandler.initializeDefaultBrush()
    }

    // 处理事件的统一入口
    val handleEvent: (MenuEvent) -> Unit = { event ->
        popupState = eventHandler.handleEvent(event, popupState, menuBarState, lastDismissTime)
    }

    // 关闭弹窗的统一处理
    val dismissPopup: () -> Unit = {
        lastDismissTime = System.currentTimeMillis()
        popupState = MenuPopupState.None
        setPopupComposable(null)
    }

    // 弹窗管理器 - 根据状态显示对应的弹窗
    TabletMenuPopupManager(
        popupState = popupState,
        penToolbarViewModel = penToolbarViewModel,
        eventHandler = eventHandler,
        setPopupComposable = setPopupComposable,
        handleEvent = handleEvent,
        dismissPopup = dismissPopup
    )

    val menuToolItems = remember(
        menuBarState.isKeyboardActive,
        menuBarState.isBrushActive,
        menuBarState.isEraserActive,
        menuBarState.isLassoActive,
        menuBarState.isRulerActive,
        menuBarState.isHandwritingToTextActive
    ) {
        listOf(
            // MAIN 组 - 键盘按钮
            MenuBarItem.Keyboard.apply {
                isChecked = menuBarState.isKeyboardActive
                onClick = { handleEvent(MenuEvent.KeyboardClick) }
            },
            // EDIT 组 - 绘图工具
            MenuBarItem.Brush.apply {
                onClick = { item -> handleEvent(MenuEvent.BrushClick(item)) }
                isChecked = menuBarState.currentMenuType == MenuBar.BRUSH && menuBarState.isBrushActive
                iconRes = selectedPen.menHullIcon()
            },
            MenuBarItem.Eraser.apply {
                isChecked = menuBarState.isEraserActive
                onClick = { item -> handleEvent(MenuEvent.EraserClick(item)) }
                iconRes = com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_eraser_nor
            }
            // TOOL 组 - 特殊绘图工具（注释掉的部分保持不变）
            /* MenuBarItem.Lasso.apply {
                isChecked = isLassoActive
                onClick = {}
            },
            MenuBarItem.Ruler.apply {
                isChecked = isRulerActive
                onClick = {}
            },
            MenuBarItem.HandwritingToText.apply {
                isChecked = isHandwritingToTextActive
                onClick = {  }
            }*/
        )
    }

    val lastMenuType = menuToolItems.last().menuType

    // 渲染上面数据源里加的菜单项
    Row(
        modifier = Modifier.height(dimens.menuBarHeight),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        menuToolItems.forEachIndexed { index, item ->
            val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
                val position = layoutCoordinates.localToWindow(Offset.Zero)
                item.position = position
            }

            // 除了第一个按钮，其他按钮前面都加间距
            if (index > 0) {
                Spacer(Modifier.width(16.dp))
            }

            if (item.menuType == MenuBar.BRUSH) {
                PenTurnColorButton(
                    modifier = modifierPos,
                    btnSize = item.btnSize,
                    iconSize = dimens.iconSize,
                    isChecked = item.isChecked,
                    onClick = { item.onClick.invoke(item) },
                    hullIconRes = selectedPen.menHullIcon(),
                    nibIconRes = selectedPen.menNibIcon(),
                    contentDescription = stringResource(item.descriptionRes),
                    penColor = selectedPen.color,
                )
            } else {
                DelayedBackgroundIconButton(
                    modifier = modifierPos,
                    btnSize = item.btnSize,
                    painter = painterResource(item.iconRes!!),
                    isChecked = item.isChecked,
                    enabled = item.isEnabled,
                    contentDescription = stringResource(item.descriptionRes),
                    onClick = { item.onClick.invoke(item) }
                )
            }

            if (item.menuType == MenuBar.KEYBOARD) {
                Spacer(Modifier.width(16.dp))
                VerticalLine()  // 键盘按钮后添加分隔线
            } else if (item.menuType == lastMenuType) {
                Spacer(Modifier.width(16.dp)) // 分组间距
            }
        }
    }
}