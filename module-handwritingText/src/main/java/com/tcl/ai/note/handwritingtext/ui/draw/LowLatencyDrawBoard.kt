package com.tcl.ai.note.handwritingtext.ui

import android.annotation.SuppressLint
import android.view.MotionEvent
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.util.fastForEachIndexed
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.tcl.ai.note.handwritingtext.bean.*
import com.tcl.ai.note.handwritingtext.lowlatency.gl.FastRender
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.DrawingBeautifyViewModel
import com.tcl.ai.note.theme.EraserCircleColor
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.widget.capturable.GraphicsLayerController
import com.tcl.ai.note.widget.capturable.recordGraphicsLayer
import kotlinx.coroutines.*

@Deprecated("废弃，使用SuniaDrawBoard替换")
@SuppressLint("ClickableViewAccessibility")
@Composable
fun LowLatencyDrawBoard(
    noteId: Long,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    onNewDrawing: () -> Unit = {},
    onTouch: (motionEvent: MotionEvent) -> Unit = {},
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    beautifyViewModel: DrawingBeautifyViewModel = hiltViewModel(),
    graphicsLayerController: GraphicsLayerController? = null,
) {
    val scope = rememberCoroutineScope()
    val fastRender = remember {
        FastRender(
            drawBoardViewModel = drawBoardViewModel,
            onNewDrawing = onNewDrawing,
            onTouch = onTouch
        )
    }

    LaunchedEffect(noteId) {
        drawBoardViewModel.updateNoteIdAndLoadStrokes(noteId)
    }

    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_PAUSE) {
                drawBoardViewModel.clearFastRender()
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    with(drawBoardViewModel) {
        LaunchedEffect(canvasDrawEventFlow) {
            withContext(Dispatchers.IO) {
                canvasDrawEventFlow.collect { event ->
                    val motionEvent = event.motionEvent
                    val point = event.drawPoint
                    when (motionEvent) {
                        DrawMotionEvent.IDLE -> {}

                        DrawMotionEvent.DOWN -> {
                            currentCanvasDrawPoints.clear()
                            currentCanvasDrawPoints.add(point)
                            // flow内能保证被清空
                            currentCanvasDrawPath.rewind()
                            currentMotionEvent = DrawMotionEvent.DOWN
                            currentPosition = point.toOffset()
                            currentCanvasDrawPath.moveTo(point.x, point.y)
                            lastPosition = currentPosition
                        }

                        DrawMotionEvent.MOVE -> {
                            currentCanvasDrawPoints.add(point)
                            currentMotionEvent = DrawMotionEvent.MOVE
                            lastPosition = currentPosition
                            currentPosition = point.toOffset()
                            if (currentCanvasDrawPath.isEmpty) {
                                currentCanvasDrawPath.moveTo(point.x, point.y)
                            } else {
                                val center = (lastPosition + point.toOffset()) / 2f
                                currentCanvasDrawPath.quadraticTo(
                                    lastPosition.x,
                                    lastPosition.y,
                                    center.x,
                                    center.y,
                                )
                                currentCanvasDrawPath = currentCanvasDrawPath.copy()
                            }
                        }

                        DrawMotionEvent.UP -> {
                            currentCanvasDrawPoints.add(point)
                            addPaths(currentCanvasDrawPoints, strokeStyle.copy(), false)
                            currentCanvasDrawPoints.clear()
                            currentMotionEvent = DrawMotionEvent.IDLE
                            currentCanvasDrawPath.rewind()
                            lastPosition = Offset.Unspecified
                            currentPosition = Offset.Unspecified
                        }
                    }
                }
            }
        }
    }

    Box(
        modifier = modifier,
    ) {
        var beautifiedStrokes by remember { mutableStateOf(emptyMap<Long, DrawStroke>()) }
        LaunchedEffect(noteId, drawBoardViewModel.displayPaths.size) {
            val drawStrokeList = arrayListOf<DrawStroke>()
            drawBoardViewModel.displayPaths.forEach {
                drawStrokeList.add(it.toDrawStroke())
            }

            scope.launchIO {
                // 5s 强制美化笔迹
                delay(5000)
                // beautifiedStrokes = beautifyViewModel.pushPendingDrawStrokes(noteId, drawStrokeList)
            }

            launch {
                // 抬手500ms美化笔迹
                var startTime = System.currentTimeMillis()
                while (true) {
                    if (fastRender.isDrawing()) {
                        startTime = System.currentTimeMillis()
                    }
                    if (System.currentTimeMillis() - startTime > 500) {
                        break
                    }
                    delay(1)
                }
                // beautifiedStrokes = beautifyViewModel.pushPendingDrawStrokes(noteId, drawStrokeList)
            }
        }

        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clipToBounds()
                .then(
                    if (graphicsLayerController == null) Modifier else Modifier.recordGraphicsLayer(
                        graphicsLayerController
                    )
                )
        ) {
            withTransform({
                with(drawBoardViewModel) {
                    translate(
                        translation.x,
                        translation.y
                    )
                    scale(
                        scale,
                        scale,
                        Offset(0f, 0f)
                    )
                }
            }) {
                with(drawContext.canvas.nativeCanvas) {
                    val checkPoint = saveLayer(null, null)
                    // 先画持久层，避免后添加的笔画被放在底层
                    drawBoardViewModel.displayPaths.fastForEachIndexed { index, display ->
                        // 优先有美化后的字迹，如果没有就用原始字迹
                        val drawPathDisplay =
                            beautifiedStrokes[display.id]?.toDrawPathDisplay() ?: display
                        drawPathDisplay.draw(this@withTransform)

                        // 防止最后一笔和美化笔同时重叠
                        if (index == drawBoardViewModel.displayPaths.lastIndex) {
                            val beautifiedStroke = beautifiedStrokes[display.id]
                            if (beautifiedStroke != null && !fastRender.isDrawing()) {
                                drawBoardViewModel.clearFastRender()
                            }
                        }
                    }

                    // Canvas绘制
                    with(drawBoardViewModel) {
                        // when (currentMotionEvent) {
                        //     // 必须在compose内moveTo，否则不触发重绘
                        //     DrawMotionEvent.DOWN -> {
                        //         currentCanvasDrawPath.moveTo(
                        //             currentPosition.x,
                        //             currentPosition.y
                        //         )
                        //     }
                        //
                        //     DrawMotionEvent.MOVE -> {
                        //         // 判断为空，避免DOWN事件没接收导致笔画出错
                        //         if (currentCanvasDrawPath.isEmpty) {
                        //             currentCanvasDrawPath.moveTo(
                        //                 currentPosition.x,
                        //                 currentPosition.y,
                        //             )
                        //         } else {
                        //             if (lastPosition == Offset.Unspecified) {
                        //                 currentCanvasDrawPath.lineTo(
                        //                     currentPosition.x,
                        //                     currentPosition.y,
                        //                 )
                        //             } else {
                        //                 val center = (lastPosition + currentPosition) / 2f
                        //                 currentCanvasDrawPath.quadraticTo(
                        //                     lastPosition.x,
                        //                     lastPosition.y,
                        //                     center.x,
                        //                     center.y,
                        //                 )
                        //             }
                        //         }
                        //     }
                        //
                        //     else -> {
                        //         lastPosition = Offset.Unspecified
                        //         currentCanvasDrawPath.rewind()
                        //     }
                        // }
                        drawPath(
                            path = currentCanvasDrawPath,
                            color =
                            if (strokeStyle.drawMode == DrawMode.ERASER)
                                Color.Transparent
                            else
                                Color(strokeStyle.color),
                            style = Stroke(
                                width = strokeStyle.width,
                                cap = strokeStyle.doodlePen.strokeCap.toStrokeCap(),
                                join = strokeStyle.doodlePen.strokeJoin.toStrokeJoin(),
                            ),
                            blendMode = when {
                                strokeStyle.drawMode == DrawMode.ERASER -> BlendMode.Clear
                                else -> BlendMode.SrcOver
                            }
                        )
                        val tmpEraserPoint = eraserCurrentPoint
                        if (strokeStyle.drawMode == DrawMode.ERASER
                            && tmpEraserPoint != Offset.Unspecified
                        ) {
                            drawCircle(
                                color = EraserCircleColor,
                                center = tmpEraserPoint,
                                radius = strokeStyle.width / 2,
                                style = Stroke(2f)
                            )
                        }
                    }
                    // 状态回退
                    restoreToCount(checkPoint)
                }
            }
        }

        // size变化，重构实时手绘
        // 跑monkey不显示手绘，避免疯狂报crash(实际没有闪退，已经捕获)
/*        if (!ActivityManager.isUserAMonkey()) {
            var resetLowLatencyDrawBoard by remember { mutableStateOf(true) }
            var lowLatencyDrawBoardSize by remember { mutableStateOf(IntSize.Zero) }
            if (resetLowLatencyDrawBoard) {
                resetLowLatencyDrawBoard = false
            } else {
                AndroidView(
                    factory = { context ->
                        LowLatencySurfaceView(context, fastRenderer = fastRender)
                    },
                    modifier = Modifier
                        .onSizeChanged {
                            if (lowLatencyDrawBoardSize != it) {
                                lowLatencyDrawBoardSize = it
                                resetLowLatencyDrawBoard = true
                            }
                        }
                        .fillMaxSize()
                        .clearAndSetSemantics { },
                    update = { view ->
                        view.setOnTouchListener(
                            if (enabled) {
                                fastRender.onTouchListener
                            } else {
                                View.OnTouchListener { _, motionEvent ->
                                    onTouch(motionEvent)
                                    false
                                }
                            }
                        )
                    }
                )
            }
        }*/
    }
}

private const val TAG = "LowLatencyDrawBoard"