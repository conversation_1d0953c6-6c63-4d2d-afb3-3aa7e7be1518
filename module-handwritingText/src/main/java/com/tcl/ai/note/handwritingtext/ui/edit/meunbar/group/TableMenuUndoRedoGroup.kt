package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.group

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.vm.MenuBarUiState
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.VerticalLine


@Composable
fun TableMenuUndoRedoGroup(
    menuBarState: MenuBarUiState,
    onUndoClick: () -> Unit = {},
    onRedoClick: () -> Unit = {}
) {
    val canUndo = menuBarState.canUndo
    val canRedo = menuBarState.canRedo
    val dimens = getGlobalDimens()
    val menuItems = remember(canUndo, canRedo, onUndoClick, onRedoClick) {
        val menuBars = listOf(
            MenuBarItem.Undo.apply {
                isEnabled = canUndo
                onClick = { onUndoClick() }
            },
            MenuBarItem.Redo.apply {
                isEnabled = canRedo
                onClick = { onRedoClick() }
            })
        menuBars
    }
    Row(
        modifier = Modifier.height(dimens.menuBarHeight),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ){
        VerticalLine(
            modifier = Modifier.height(20.dp)
        )
        Spacer(Modifier.width(12.dp))
        menuItems.forEachIndexed { index, item ->
            DelayedBackgroundIconButton(
                btnSize = item.btnSize,
                iconSize = dimens.iconSize,
                painter = painterResource(item.iconRes!!),
                isChecked = item.isChecked,
                enabled = item.isEnabled,
                contentDescription = stringResource(item.descriptionRes),
                onClick = { item.onClick.invoke(item) }
            )
            if(index == 0){
                Spacer(Modifier.width(8.dp))
            }
        }
        Spacer(Modifier.width(12.dp))
    }
}