package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup

import android.annotation.SuppressLint
import android.graphics.BlurMaskFilter
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.stringRes


// 实现高斯模糊效果的Modifier扩展
fun Modifier.blurredBackground(
    color: Color = Color.White, // copy(alpha = 0.9f), 实际90%不透明度很难看
    blurRadius: Float = 8f // 高斯模糊半径
): Modifier = this.drawBehind {
    drawIntoCanvas { canvas ->
        val paint = Paint()
        val frameworkPaint = paint.asFrameworkPaint()
        frameworkPaint.color = color.toArgb()
        
        // 应用高斯模糊效果
        frameworkPaint.maskFilter = BlurMaskFilter(blurRadius, BlurMaskFilter.Blur.NORMAL)
        
        // 绘制带有高斯模糊的背景矩形
        canvas.drawRect(
            0f,
            0f,
            size.width,
            size.height,
            paint
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("DesignSystem")
@Composable
fun EraserToolBar(
    modifier: Modifier = Modifier,
    width: Dp = 339.dp,
    onClearAll: () -> Unit = {},
    onEraserSizeChange: (Float, EraserMode) -> Unit = { _, _ -> }
) {
    // 选中的模式，0=Point, 1=Stroke
    var selectedTab by remember { mutableIntStateOf(0) }
    // 橡皮擦大小 0.01~1.0，使用0.01到1的范围
    var sliderValue by remember { mutableFloatStateOf(1.0f) }

    // 实际橡皮擦大小（像素）
    val eraserSize = remember(sliderValue) {
        // 根据滑块值计算实际橡皮擦大小（像素）
        // 从1像素到100像素线性映射
        1f + (100f - 1f) * sliderValue
    }

    // 获取当前选择的橡皮擦模式
    val eraserMode = remember(selectedTab) {
        if (selectedTab == 0) EraserMode.AREA else EraserMode.STROKE
    }

    // 获取density在Composable上下文中
    val density = LocalDensity.current

    // 使用LaunchedEffect监听关键值的变化并通知上层
    LaunchedEffect(sliderValue, eraserMode) {
        // 直接使用像素值，而不是dp转换
        onEraserSizeChange(eraserSize, eraserMode)
    }

    // 外层容器 - 带高斯模糊效果
    Box(
        modifier = modifier
            .width(width)
            .heightIn(min = 95.dp) // 设置最小高度为95dp
            .defShadow()
            .clip(RoundedCornerShape(16.dp))
            //.blurredBackground() // 使用自定义的高斯模糊背景
            .background(color = TclTheme.colorScheme.reWriteExpandBg)
            .padding(horizontal = 16.dp, vertical = 12.dp) // 调整内边距
    ) {
       /* Column {
            // Tab Row - 浅灰色背景
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(16.dp))
                    .background(Color(0xFFE5E5EA))
                    .padding(4.dp)
            ) {
                Row(modifier = Modifier.fillMaxWidth()) {
                    // 使用自定义interactionSources实现更快的点击响应
                    val pointInteractionSource = remember { MutableInteractionSource() }
                    val strokeInteractionSource = remember { MutableInteractionSource() }

                    // Point Tab
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .clip(RoundedCornerShape(12.dp))
                            .background(if (selectedTab == 0) Color.White else Color.Transparent)
                            .clickable(
                                interactionSource = pointInteractionSource,
                                indication = null // 禁用默认水波纹
                            ) { selectedTab = 0 }
                            .padding(vertical = 10.dp), // 稍微减小垂直内边距
                        contentAlignment = Alignment.Center
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            // 黑点 - 未选中时使用灰色
                            Box(
                                modifier = Modifier
                                    .size(15.dp)
                                    .background(
                                        if (selectedTab == 0) Color.Black else Color(0xFF8A8A8F),
                                        CircleShape
                                    )
                            )
                            Spacer(Modifier.width(10.dp))
                            // Point Tab
                            Text(
                                "Point",
                                color = if (selectedTab == 0) Color.Black else Color(0xFF8A8A8F),
                                fontWeight = FontWeight.Medium,
                                fontSize = 16.sp
                            )
                        }
                    }

                    Spacer(Modifier.width(4.dp))

                    // Stroke Tab
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .clip(RoundedCornerShape(12.dp))
                            .background(if (selectedTab == 1) Color.White else Color.Transparent)
                            .clickable(
                                interactionSource = strokeInteractionSource,
                                indication = null // 禁用默认水波纹
                            ) { selectedTab = 1 }
                            .padding(vertical = 10.dp), // 稍微减小垂直内边距
                        contentAlignment = Alignment.Center
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_menu_eraser_stroke),
                                contentDescription = null,
                                tint = if (selectedTab == 1) Color.Black else Color(0xFF8A8A8F),
                                modifier = Modifier.size(18.dp)
                            )
                            Spacer(Modifier.width(10.dp))
                            // "Stroke" 文字
                            Text(
                                "Stroke",
                                color = if (selectedTab == 1) Color.Black else Color(0xFF8A8A8F),
                                fontWeight = FontWeight.Medium,
                                fontSize = 16.sp
                            )
                        }
                    }
                }
            }

            Spacer(Modifier.height(10.dp))

            // 下半部分 - 滑块和控件
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(20.dp) // 固定高度为20dp，与子组件一致
            ) {
                // 滑块
                Box(
                    modifier = Modifier
                        .width(141.dp)
                        .height(20.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0x1A000000))
                ) {
                    Slider(
                        value = sliderValue,
                        onValueChange = { sliderValue = it },
                        valueRange = 0.01f..1f,
                        steps = 0,
                        modifier = Modifier.padding(horizontal = 2.dp),
                        colors = SliderDefaults.colors(
                            thumbColor = Color.White,
                            activeTrackColor = Color.Transparent,
                            inactiveTrackColor = Color.Transparent
                        ),
                        // 自定义滑块为长方形
                        thumb = {
                            Box(
                                Modifier
                                    .width(8.22.dp)
                                    .height(18.dp)
                                    .shadow(1.dp, RoundedCornerShape(2.dp))
                                    .clip(RoundedCornerShape(2.dp))
                                    .background(Color.White)
                            )
                        }
                    )
                }

                Spacer(Modifier.width(4.dp))

                // 圆环展示 - 方形背景
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color(0x0D000000)),
                    contentAlignment = Alignment.Center
                ) {
                    // 绘制圆环 - 最大状态下padding为2dp
                    Canvas(modifier = Modifier.size(16.dp)) {
                        // 最大状态时圆环距离边界2dp，即半径为(16-4)/2=6dp
                        // 最小状态时保持一定大小，比如半径为2dp
                        val minRadius = 2.dp.toPx()
                        val maxRadius = 6.dp.toPx()
                        val radius = minRadius + (maxRadius - minRadius) * sliderValue

                        drawCircle(
                            color = Color(0x4D000000),
                            radius = radius,
                            style = Stroke(width = 1.dp.toPx())
                        )
                    }
                }

                Spacer(Modifier.width(12.dp))

                // 百分比显示 - 方矩形
                Box(
                    modifier = Modifier
                        .width(36.dp)
                        .height(20.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0x0D000000)),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        // 从1%到100%显示，与滑块值对应
                        "${(sliderValue * 100).roundToInt()}%",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Normal,
                        fontFamily = FontFamily.Default,
                        color = Color(0xFF3D3D3D),
                        textAlign = TextAlign.Center,
                        maxLines = 1, // 限制为单行
                        modifier = Modifier.padding(start = 5.dp)
                    )
                }

                Spacer(Modifier.width(14.dp))

                // CLEAR ALL 按钮 - 使用Material3 Surface实现点击效果
                Surface(
                    modifier = Modifier
                        .wrapContentHeight(), // 固定高度与其他元素一致
                    shape = RoundedCornerShape(3.dp),
                    color = Color.Transparent,
                    onClick = onClearAll
                ) {
                    Box(
                        modifier = Modifier
                            .padding(horizontal = 0.dp)
                            .height(16.dp), // 限制内部Box高度
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            com.tcl.ai.note.base.R.string.clear_all.stringRes(),
                            color = Color(0xFF396AF6),
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp,
                            fontFamily = FontFamily.Default,
                            textAlign = TextAlign.Center,
                            maxLines = 1, // 限制为单行文本
                            lineHeight = 16.sp, // 减小行高
                            modifier = Modifier.heightIn(max = 16.dp) // 限制文本高度
                        )
                    }
                }
            }

        }*/
        Surface(
            modifier = Modifier
                .wrapContentHeight(), // 固定高度与其他元素一致
            shape = RoundedCornerShape(3.dp),
            color = Color.Transparent,
            onClick = onClearAll
        ) {
            Box(
                modifier = Modifier
                    .padding(horizontal = 0.dp)
                    .wrapContentHeight(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    com.tcl.ai.note.base.R.string.clear_all.stringRes(),
                    color = Color(0xFF396AF6),
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp,
                    fontFamily = FontFamily.Default,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                )
            }
        }
    }
}