package com.tcl.ai.note.handwritingtext.ui.pen

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.Indication
import androidx.compose.foundation.LocalIndication
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.indication
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.HoverMutableInteractionSource
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import androidx.compose.material3.LocalUseFallbackRippleImplementation
import androidx.compose.material3.ripple
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.semantics.Role
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.ripple.rippleOrFallbackImplementation
import com.tcl.ai.note.utils.drawableRes
import kotlinx.coroutines.delay


@Composable
fun PenTurnColorButton(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    modifier: Modifier = Modifier,
    btnSize: Dp,
    iconSize:Dp,
    onClick: () -> Unit,
    isChecked: Boolean = false,
    @DrawableRes hullIconRes: Int,
    @DrawableRes nibIconRes: Int,
    contentDescription: String?,
    penColor: Color,


) {
    var showBackground by remember { mutableStateOf(false) }
    val hoverInteraction = remember {
        HoverMutableInteractionSource()
    }

    LaunchedEffect(isChecked) {
        if (isChecked) {
            delay(300)
            showBackground = true
        } else {
            showBackground = false
        }
    }

    Box(
        modifier = modifier
            .size(btnSize)
            .clickable (
                onClick = onClick,
                role = Role.Button,
                interactionSource = hoverInteraction,
                indication =
                rippleOrFallbackImplementation(
                        bounded = false,
                        radius = btnSize / 2
                    )
            )
            .then(
                showBackground.judge(
                    Modifier.background(
                        color = R.color.tablet_btn_checked_bg.colorRes(),
                        shape = RoundedCornerShape(btnSize / 2),
                    ),
                    Modifier
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        val tintColor=if(isDarkTheme) R.color.white else R.color.transparent_icon
        Image(
            modifier = Modifier.size(iconSize),
            painter = nibIconRes.drawableRes(),
            contentDescription = contentDescription,
            colorFilter =isDarkTheme.judge(
                ColorFilter.tint(penColor.inverseColor()),
                ColorFilter.tint((penColor== Color.White).judge(Color.Transparent, penColor))
            ),
        )
        Image(
            painter = hullIconRes.drawableRes(),
            contentDescription = contentDescription,
            colorFilter = ColorFilter.tint(tintColor.colorRes()),
        )
    }
}
