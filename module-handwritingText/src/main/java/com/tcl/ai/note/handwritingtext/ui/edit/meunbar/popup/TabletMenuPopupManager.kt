package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.state.MenuEvent
import com.tcl.ai.note.handwritingtext.state.MenuPopupState
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.handler.TabletMenuEventHandler
import com.tcl.ai.note.handwritingtext.ui.popup.EraserToolPopup
import com.tcl.ai.note.handwritingtext.ui.popup.TabletPenColorPalettePopup
import com.tcl.ai.note.handwritingtext.ui.popup.TabletToolPopup
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel

/**
 * 平板菜单弹窗管理器
 * 
 * 根据弹窗状态显示相应的弹窗组件
 * 职责：弹窗的显示和隐藏逻辑
 */
@Composable
fun TabletMenuPopupManager(
    popupState: MenuPopupState,
    penToolbarViewModel: PenToolbarViewModel,
    eventHandler: TabletMenuEventHandler,
    setPopupComposable: ((@Composable () -> Unit)?) -> Unit,
    handleEvent: (MenuEvent) -> Unit,
    dismissPopup: () -> Unit
) {
    val selectedPen = penToolbarViewModel.selectedPen

    // 直接响应状态变化，Compose 会自动重组
    when (popupState) {
        is MenuPopupState.None -> {
            setPopupComposable(null)
        }
        
        is MenuPopupState.EraserTool -> {
            setPopupComposable {
                EraserToolPopup(
                    menuBarItem = popupState.menuBarItem,
                    onDismissRequest = dismissPopup,
                    onSwitchToBrush = eventHandler::activateBrushTool
                )
            }
        }
        
        is MenuPopupState.PenTool -> {
            setPopupComposable {
                var isShowColorPicker  by remember { mutableStateOf(false) }
                TabletToolPopup(
                    menuBarItem = popupState.menuBarItem,
                    onOpenColorPicker = {
                        isShowColorPicker = true
                    },
                    onDismissRequest = dismissPopup,
                    onSwitchBrush = eventHandler::switchBrush,
                    onChangeBrushSize = eventHandler::changeBrushSize,
                    onChangePenColor = eventHandler::changePenColor
                )
                if (isShowColorPicker) {
                    TabletPenColorPalettePopup(
                        menuBarItem = popupState.menuBarItem,
                        curPenColor = PenColor(
                            color = selectedPen.color,
                            alpha = selectedPen.alpha
                        ),
                        onConfirm = { penColor ->
                            eventHandler.confirmColorSelection(penColor)
                            dismissPopup()
                        },
                        onDismissRequest = {
                            isShowColorPicker =false
                        }
                    )
                }
            }
        }else ->{}
        

    }
} 