package com.tcl.ai.note.handwritingtext.ui.pen

import android.util.Log
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateIntOffsetAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.drawableRes
import com.tcl.ai.note.widget.LongPressIconButton
import kotlinx.coroutines.delay
import kotlin.math.roundToInt


/**
 * 笔刷大小滑动条
 */
@Composable
fun BrushSizeSlider(
    modifier: Modifier = Modifier,
    lastProgress: Int,
    onProgressChanged:(Int) -> Unit
) {
    val density = LocalDensity.current
    val sliderHeight = 20.dp
    val sliderWidth = 160.dp
    val handleSize = 14.dp
    val minProgress =1
    val maxProgress =100


    val context = LocalContext.current
    val horizontalPadding = 3.dp
    val sliderWidthPx = with(density) { ((sliderWidth - handleSize-horizontalPadding*2).toPx()) }
    val handleSizePx = with(density) { (sliderHeight - handleSize).toPx() }

    var curProgress by remember { mutableIntStateOf(lastProgress) }
    var isLongPressingLess by remember { mutableStateOf(false) }
    var isLongPressingMore by remember { mutableStateOf(false) }

    val addBrushSize:() -> Unit = {
        curProgress = (curProgress + 1).coerceAtMost(100)
        onProgressChanged(curProgress)


    }
    val lessBrushSize:() -> Unit = {
        curProgress = (curProgress - 1).coerceAtLeast(1)
        onProgressChanged(curProgress)

    }

    LaunchedEffect(lastProgress) {
        if (!isLongPressingLess && !isLongPressingMore) {
            curProgress = lastProgress
        }
    }

    LaunchedEffect(isLongPressingLess) {
        while (isLongPressingLess) {
            lessBrushSize()
            delay(100)
        }
    }
    LaunchedEffect(isLongPressingMore) {
        while (isLongPressingMore) {
            addBrushSize()
            delay(100)
        }
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(start = 8.dp, end = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        LongPressIconButton(
            onLongPress = { isLongPressing ->
                isLongPressingLess = isLongPressing
            },
            onClick = {
                lessBrushSize()
            },
            modifier = Modifier.size(32.dp)
                .clearAndSetSemantics {
                    role = Role.Button
                    contentDescription = context.getString(com.tcl.ai.note.base.R.string.brush_size_less)
                },
        ) {
            Icon(
                painter = R.drawable.ic_value_decrease.drawableRes(),
                contentDescription = ""
            )
        }

        Box(
            modifier = Modifier.width(sliderWidth).height(sliderHeight)
                .semantics {
                    contentDescription= String.format(context.getString(com.tcl.ai.note.base.R.string.brush_slider_status),"${lastProgress}%")
                }
        ){
            RoundedBarWithDots(modifier = Modifier
                .fillMaxWidth()
                .height(sliderHeight)
                .clip(RoundedCornerShape(sliderHeight/2))
                .align(Alignment.Center))
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .padding(horizontal = horizontalPadding)
                    .pointerInput(Unit) {
                        awaitEachGesture {
                            while (true) {
                                val event = awaitPointerEvent()
                                val finger = event.changes.firstOrNull() ?: break
                                if (finger.pressed){
                                    val pos = finger.position
                                    val x = pos.x.coerceIn(0f, sliderWidthPx)
                                    val ratio = (x / sliderWidthPx).coerceIn(0f, 1f)

                                    curProgress = (ratio * (maxProgress - minProgress) + minProgress).roundToInt()
                                }else{
                                    onProgressChanged(curProgress)
                                }

                            }
                        }
                    }
            ){
                val handleOffsetX = (curProgress - minProgress) / (maxProgress - minProgress).toFloat() * sliderWidthPx
                if(handleOffsetX>=0){

                    val animatedOffset by animateIntOffsetAsState(
                        targetValue = IntOffset(
                            x = handleOffsetX.roundToInt(),
                            y = handleSizePx.div(2).roundToInt()
                        ),
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioLowBouncy,
                            stiffness = Spring.StiffnessMedium
                        ),
                        label = "handle_offset_animation"
                    )

                    Box(
                        Modifier
                            .offset {
                                animatedOffset
                            }
                            .size(handleSize)
                            .clip(CircleShape)
                            .background(R.color.brush_handle_bg.colorRes())
                    )

                }
            }


        }
        LongPressIconButton(
            onLongPress = { isLongPressing ->
                isLongPressingMore = isLongPressing
            },
            onClick = {
                addBrushSize()
            },
            modifier = Modifier
                .size(32.dp)
                .clearAndSetSemantics {
                    role = Role.Button
                    contentDescription = context.getString(com.tcl.ai.note.base.R.string.brush_size_add)
                }
        ) {
            Icon(
                painter = R.drawable.ic_value_increase.drawableRes(),
                contentDescription = ""
            )
        }
        BrushSizeIndicator(curProgress)

    }
}

@Composable
internal fun BrushSizeIndicator(
    curProgress:Int
){
    val density = LocalDensity.current

    Box(
        modifier = Modifier
            .width(36.dp)
            .height(24.dp)
            .border(
                width = 1.dp,
                color = R.color.slider_indicator_border.colorRes(),
                shape = RoundedCornerShape(4.dp)
            ),
        contentAlignment = Alignment.Center
    ){
        Text(
            text = "$curProgress",
            textAlign = TextAlign.Center,
            color = R.color.slider_indicator.colorRes(),
            fontSize = with(density) { 12.dp.toSp() },
            lineHeight = with(density) { 14.dp.toSp() },
        )
    }

}


@Composable
fun RoundedBarWithDots(modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .background(R.color.popup_brush_slider_bg.colorRes())
            .padding(horizontal = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(5) {
            Box(
                modifier = Modifier
                    .size(4.dp)
                    .background(color = R.color.brush_size_dot.colorRes(), shape = CircleShape)
            )
        }
    }
}
