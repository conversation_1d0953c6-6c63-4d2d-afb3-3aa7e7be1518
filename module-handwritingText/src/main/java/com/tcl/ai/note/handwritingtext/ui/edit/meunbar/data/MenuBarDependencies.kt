package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data

import androidx.compose.runtime.Composable
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.ColorGroupViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextToolBarViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel

/**
 * 菜单栏依赖项组合类
 * 将多个ViewModel组合到一起，减少函数参数数量
 * 包含状态管理能力
 */
data class MenuBarDependencies(
    val textAndDrawViewModel: TextAndDrawViewModel,
    val richTextToolBarViewModel: RichTextToolBarViewModel,
    val menuBarViewModel: MenuBarViewModel,
    val suniaDrawViewModel: SuniaDrawViewModel,
    val penToolbarViewModel: PenToolbarViewModel,
    val colorGroupViewModel: ColorGroupViewModel,
    val richTextViewModel: RichTextViewModel2,
    val audioToTextViewModel: AudioToTextViewModel,
    val recordingViewModel: RecordingViewModel,
    val eraserViewModel: EraserViewModel
) {
    
    /**
     * 状态管理器（懒加载）
     */
    val stateManager: MenuBarStateManager by lazy {
        MenuBarStateManager(this)
    }
    companion object {
        @Composable
        fun createDefault(): MenuBarDependencies {
            return MenuBarDependencies(
                textAndDrawViewModel = hiltViewModel(),
                richTextToolBarViewModel = hiltViewModel(),
                menuBarViewModel = hiltViewModel(),
                suniaDrawViewModel = hiltViewModel(),
                penToolbarViewModel = hiltViewModel(),
                colorGroupViewModel = hiltViewModel(),
                richTextViewModel = hiltViewModel(),
                audioToTextViewModel = hiltViewModel(),
                recordingViewModel = hiltViewModel(),
                eraserViewModel = hiltViewModel()
            )
        }

        @Composable
        fun createJournal(viewModel: SuniaDrawViewModel, textAndDrawViewModel: TextAndDrawViewModel): MenuBarDependencies {
            return MenuBarDependencies(
                textAndDrawViewModel = textAndDrawViewModel,
                richTextToolBarViewModel = hiltViewModel(),
                menuBarViewModel = hiltViewModel(),
                suniaDrawViewModel = viewModel,
                penToolbarViewModel = hiltViewModel(),
                colorGroupViewModel = hiltViewModel(),
                richTextViewModel = hiltViewModel(),
                audioToTextViewModel = hiltViewModel(),
                recordingViewModel = hiltViewModel(),
                eraserViewModel = hiltViewModel()
            )
        }
    }
}

/**
 * 菜单栏配置类
 * 包含可选的配置参数
 */
data class MenuBarConfig(
    val setPopupComposable: ((@Composable (areaHeight:Int) -> Unit)?) -> Unit = {},
    val onMenuItemClick: ((String) -> Unit)? = null,
    val enableLogging: Boolean = true
) 