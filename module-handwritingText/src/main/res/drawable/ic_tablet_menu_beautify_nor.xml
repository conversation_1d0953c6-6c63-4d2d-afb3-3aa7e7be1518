<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="28dp"
    android:height="28dp"
    android:viewportWidth="28"
    android:viewportHeight="28">
  <group>
    <clip-path
        android:pathData="M22,6l-16,0l-0,16l16,0z"/>
    <path
        android:pathData="M7.143,18.301L6.915,19.914C6.881,20.153 6.864,20.277 6.894,20.366C6.935,20.488 7.033,20.585 7.155,20.626C7.245,20.655 7.369,20.638 7.607,20.603L9.238,20.366C9.758,20.29 10.019,20.252 10.265,20.176C10.607,20.069 10.93,19.907 11.221,19.696C11.429,19.545 11.615,19.358 11.987,18.987L20.076,10.895C20.603,10.368 20.873,10.095 20.996,9.801C21.164,9.398 21.164,8.94 20.995,8.536C20.873,8.243 20.602,7.97 20.074,7.443C19.548,6.917 19.275,6.647 18.982,6.525C18.578,6.357 18.121,6.357 17.718,6.525C17.425,6.647 17.152,6.917 16.625,7.443L8.527,15.532C8.153,15.905 7.964,16.094 7.812,16.303C7.6,16.595 7.437,16.921 7.331,17.267C7.255,17.514 7.217,17.777 7.143,18.301ZM8.435,18.431L8.34,18.908C8.315,19.038 8.302,19.104 8.322,19.15C8.338,19.189 8.369,19.22 8.408,19.236C8.454,19.256 8.521,19.243 8.65,19.217L9.142,19.117C9.575,19.03 9.793,18.986 9.995,18.907C10.173,18.836 10.343,18.745 10.501,18.635C10.679,18.511 10.836,18.354 11.148,18.042L17.386,11.803L15.758,10.176L9.515,16.409C9.2,16.723 9.042,16.881 8.917,17.061C8.806,17.22 8.715,17.391 8.644,17.571C8.565,17.775 8.521,17.994 8.435,18.431ZM16.702,9.234L18.329,10.861L19.519,9.671C19.681,9.508 19.764,9.425 19.794,9.331C19.819,9.25 19.819,9.163 19.794,9.082C19.764,8.988 19.681,8.905 19.519,8.742L18.821,8.045C18.659,7.882 18.575,7.8 18.482,7.77C18.401,7.744 18.314,7.744 18.233,7.77C18.139,7.8 18.056,7.882 17.893,8.044L16.702,9.234Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="7.064"
            android:startY="13.52"
            android:endX="20.91"
            android:endY="13.52"
            android:type="linear">
          <item android:offset="0" android:color="#FFD798FA"/>
          <item android:offset="1" android:color="#FF4D6BF2"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M15.475,18.06C15.547,17.886 15.787,17.886 15.859,18.06L15.965,18.315C16.146,18.752 16.484,19.101 16.908,19.29L17.208,19.423C17.376,19.498 17.376,19.742 17.208,19.816L16.891,19.957C16.477,20.141 16.145,20.478 15.961,20.9L15.858,21.136C15.784,21.305 15.55,21.305 15.477,21.136L15.374,20.9C15.189,20.478 14.858,20.141 14.444,19.957L14.126,19.816C13.959,19.742 13.959,19.498 14.126,19.423L14.426,19.29C14.851,19.101 15.188,18.752 15.369,18.315L15.475,18.06Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14.044"
            android:startY="19.596"
            android:endX="17.284"
            android:endY="19.596"
            android:type="linear">
          <item android:offset="0" android:color="#FFD798FA"/>
          <item android:offset="1" android:color="#FF4D6BF2"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M9.028,9.113C9.105,8.928 9.361,8.928 9.437,9.113L9.55,9.385C9.743,9.85 10.102,10.222 10.554,10.423L10.873,10.564C11.052,10.644 11.052,10.903 10.873,10.983L10.535,11.133C10.095,11.329 9.741,11.687 9.545,12.137L9.436,12.388C9.357,12.568 9.108,12.568 9.03,12.388L8.92,12.137C8.724,11.687 8.371,11.329 7.93,11.133L7.592,10.983C7.414,10.903 7.414,10.644 7.592,10.564L7.911,10.423C8.363,10.222 8.723,9.85 8.916,9.385L9.028,9.113Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="7.505"
            android:startY="10.749"
            android:endX="10.954"
            android:endY="10.749"
            android:type="linear">
          <item android:offset="0" android:color="#FFD798FA"/>
          <item android:offset="1" android:color="#FF4D6BF2"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.9,12.999C18.947,12.886 19.103,12.886 19.15,12.999L19.219,13.166C19.337,13.45 19.558,13.678 19.834,13.801L20.03,13.888C20.139,13.936 20.139,14.096 20.03,14.144L19.823,14.236C19.553,14.356 19.337,14.576 19.217,14.851L19.149,15.005C19.101,15.115 18.949,15.115 18.901,15.005L18.834,14.851C18.714,14.576 18.497,14.356 18.227,14.236L18.02,14.144C17.911,14.096 17.911,13.936 18.02,13.888L18.216,13.801C18.493,13.678 18.713,13.45 18.831,13.166L18.9,12.999Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="19.025"
            android:startY="12.914"
            android:endX="19.025"
            android:endY="15.087"
            android:type="linear">
          <item android:offset="0" android:color="#FF4968EE"/>
          <item android:offset="1" android:color="#FF7E65F7"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
