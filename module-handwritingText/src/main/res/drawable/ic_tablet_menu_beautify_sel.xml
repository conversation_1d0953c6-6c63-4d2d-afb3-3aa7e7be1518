<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="16dp"
    android:height="16dp"
    android:viewportWidth="16"
    android:viewportHeight="16">
  <group>
    <clip-path
        android:pathData="M16,0l-16,0l-0,16l16,0z"/>
    <path
        android:pathData="M1.143,12.301L0.915,13.914C0.881,14.152 0.864,14.276 0.894,14.366C0.935,14.488 1.033,14.585 1.155,14.625C1.245,14.655 1.369,14.638 1.607,14.603L3.238,14.365C3.758,14.29 4.019,14.251 4.265,14.175C4.607,14.069 4.93,13.907 5.221,13.695C5.429,13.544 5.615,13.358 5.987,12.986L14.076,4.895C14.603,4.368 14.873,4.095 14.996,3.801C15.164,3.397 15.164,2.94 14.995,2.536C14.873,2.242 14.602,1.969 14.074,1.443C13.548,0.917 13.275,0.647 12.982,0.524C12.578,0.356 12.121,0.356 11.718,0.525C11.425,0.647 11.152,0.917 10.625,1.443L2.527,9.531C2.153,9.905 1.964,10.093 1.812,10.302C1.6,10.595 1.437,10.921 1.331,11.266C1.255,11.513 1.217,11.777 1.143,12.301ZM2.435,12.43L2.34,12.908C2.315,13.037 2.302,13.104 2.322,13.149C2.338,13.188 2.369,13.219 2.408,13.236C2.454,13.255 2.521,13.242 2.65,13.216L3.142,13.117C3.575,13.03 3.793,12.986 3.995,12.906C4.173,12.836 4.343,12.745 4.501,12.635C4.679,12.51 4.836,12.354 5.148,12.041L11.386,5.803L9.758,4.175L3.515,10.408C3.2,10.723 3.042,10.881 2.917,11.06C2.806,11.219 2.715,11.39 2.644,11.571C2.565,11.774 2.521,11.993 2.435,12.43ZM10.702,3.233L12.329,4.86L13.519,3.67C13.681,3.508 13.764,3.424 13.794,3.33C13.819,3.25 13.819,3.162 13.794,3.082C13.764,2.988 13.681,2.904 13.519,2.742L12.821,2.044C12.659,1.882 12.575,1.799 12.482,1.769C12.401,1.744 12.314,1.744 12.233,1.769C12.139,1.799 12.056,1.882 11.893,2.044L10.702,3.233Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="2.436"
            android:startY="3.291"
            android:endX="14.232"
            android:endY="10.634"
            android:type="linear">
          <item android:offset="0" android:color="#FFE4F3FF"/>
          <item android:offset="1" android:color="#FFF7F6FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M9.475,12.06C9.547,11.886 9.787,11.886 9.859,12.06L9.965,12.315C10.146,12.752 10.484,13.101 10.908,13.29L11.208,13.423C11.376,13.498 11.376,13.742 11.208,13.816L10.891,13.957C10.477,14.141 10.145,14.478 9.961,14.9L9.858,15.136C9.784,15.305 9.55,15.305 9.477,15.136L9.374,14.9C9.189,14.478 8.858,14.141 8.444,13.957L8.126,13.816C7.959,13.742 7.959,13.498 8.126,13.423L8.426,13.29C8.851,13.101 9.188,12.752 9.369,12.315L9.475,12.06Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="8.365"
            android:startY="12.607"
            android:endX="11.126"
            android:endY="14.325"
            android:type="linear">
          <item android:offset="0" android:color="#FFE4F3FF"/>
          <item android:offset="1" android:color="#FFF7F6FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M3.028,3.113C3.105,2.928 3.361,2.928 3.437,3.113L3.55,3.385C3.743,3.85 4.102,4.222 4.554,4.423L4.873,4.564C5.052,4.644 5.052,4.903 4.873,4.983L4.535,5.133C4.095,5.329 3.741,5.687 3.545,6.137L3.436,6.388C3.357,6.568 3.108,6.568 3.03,6.388L2.92,6.137C2.724,5.687 2.371,5.329 1.93,5.133L1.592,4.983C1.414,4.903 1.414,4.644 1.592,4.564L1.911,4.423C2.363,4.222 2.723,3.85 2.916,3.385L3.028,3.113Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="1.847"
            android:startY="3.695"
            android:endX="4.785"
            android:endY="5.525"
            android:type="linear">
          <item android:offset="0" android:color="#FFE4F3FF"/>
          <item android:offset="1" android:color="#FFF7F6FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M12.9,6.999C12.947,6.886 13.103,6.886 13.15,6.999L13.219,7.166C13.337,7.45 13.558,7.678 13.834,7.801L14.03,7.888C14.139,7.936 14.139,8.096 14.03,8.144L13.823,8.236C13.553,8.356 13.337,8.576 13.217,8.851L13.149,9.005C13.101,9.115 12.949,9.115 12.901,9.005L12.834,8.851C12.714,8.576 12.497,8.356 12.227,8.236L12.02,8.144C11.911,8.096 11.911,7.936 12.02,7.888L12.216,7.801C12.493,7.678 12.713,7.45 12.831,7.166L12.9,6.999Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="12.176"
            android:startY="7.356"
            android:endX="13.976"
            android:endY="8.476"
            android:type="linear">
          <item android:offset="0" android:color="#FFE4F3FF"/>
          <item android:offset="1" android:color="#FFF7F6FF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
