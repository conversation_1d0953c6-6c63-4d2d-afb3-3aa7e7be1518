package com.tcl.ai.note.home.screen

import android.content.Context
import android.text.format.DateFormat
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.tcl.ai.note.dashboard.view.ROUTE_CHECK_UPDATE
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogScreenManager
import com.tcl.ai.note.home.components.base.HomeAdaptiveNavigationScreen
import com.tcl.ai.note.home.components.categorylist.HomeCategoryDrawerContent
import com.tcl.ai.note.home.components.categorylist.UpdateIconWithDot
import com.tcl.ai.note.home.components.notelist.HomeNoteListContent
import com.tcl.ai.note.home.components.NavigationTab
import com.tcl.ai.note.home.components.base.rememberTabletLeftWeight
import com.tcl.ai.note.home.vm.HomeCoordinator
import com.tcl.ai.note.home.vm.rememberHomeCoordinator
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.state.HomeTitleMode
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.setting.version.VerisonViewModel
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.startAIHelpWriting
import com.tcl.ai.note.utils.startEditActivity
import kotlinx.coroutines.launch
import okhttp3.internal.toLongOrDefault

/**
 * 主页屏幕的路由组件
 */
@Composable
fun HomeNoteScreen(
    navController: NavController,
    onToTab: (NavigationTab) -> Unit,
    coordinator: HomeCoordinator = rememberHomeCoordinator()
) {
    val versionViewMode:VerisonViewModel = hiltViewModel()
    val updateState by versionViewMode.updateState.collectAsStateWithLifecycle()
    val isCheckUpdate by versionViewMode.isCheckUpdate.collectAsStateWithLifecycle()
    val homeUiState = coordinator.homeUiState.collectAsStateWithLifecycle()
    val categoryUiState = coordinator.categoryUiState.collectAsStateWithLifecycle()
    val rememberDrawerState = rememberDrawerState(DrawerValue.Closed)
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    // 搜索模式下禁用抽屉手势，同时支持临时禁用
    var temporaryGesturesDisabled by remember { mutableStateOf(false) }
    val gesturesEnabled by remember {
        derivedStateOf { !homeUiState.value.isSearchMode && !temporaryGesturesDisabled }
    }


    // UI Events
    val actionsHandler: (HomeNoteListAction) -> Unit = { action ->
        when (action) {
            is HomeNoteListAction.OnAddHomeNoteClick ->{
//                if (action.noteId.isBlank()) {
//                    context.startAIHelpWriting(0)
//                }else{
                    context.startEditActivity(action.noteId, action.isPen)
//                }
            }

            is HomeNoteListAction.OnOpenDrawer ->
                coroutineScope.launch {
                    temporaryGesturesDisabled = true
                    try {
                        rememberDrawerState.open()
                    } finally {
                        temporaryGesturesDisabled = false
                    }
                }

            is HomeNoteListAction.OnNavigateTo ->
                onToTab(action.tab)

            else -> {}
        }
        coordinator.handleHomeAction(action)
    }
    val homeCategoryActionHandler: (HomeCategoryAction) -> Unit = { event ->
        if (event is HomeCategoryAction.OnCategorySelected) {
            coroutineScope.launch {
                rememberDrawerState.close()
            }
        }
        if (event is HomeCategoryAction.OnCheckUpdate) {
            versionViewMode.getIsCheckUpdate()
            navController.navigate(ROUTE_CHECK_UPDATE)
        }
        coordinator.handleCategoryAction(event)
    }
    if (homeUiState.value.isEditMode||homeUiState.value.isSearchMode){
        BackHandler {
            // 退出编辑模式
            actionsHandler(HomeNoteListAction.OnChangeTitleMode(HomeTitleMode.Normal))
        }
    }
    val tabletLeftWeight = rememberTabletLeftWeight()
   

    HomeAdaptiveNavigationScreen(
        isTablet = isTablet,
        leftPanelWeight = tabletLeftWeight,
        drawerState = rememberDrawerState,
        gesturesEnabled = gesturesEnabled, // 搜索模式下禁用抽屉手势
        drawerContent = {
            HomeCategoryDrawerContent(
                categoryUiState = categoryUiState.value,
                onCategoryAction = homeCategoryActionHandler,
                updateIconContent = {
                    UpdateIconWithDot(homeCategoryActionHandler,updateState,isCheckUpdate)
                }
            )
        },
        content = {
            HomeNoteListContent(modifier = Modifier.fillMaxHeight(), homeUiState.value, actionsHandler)
        })
    CategoryDialogScreenManager(screenKey = "HomeScreen")
    OnResumeEffect(context, coordinator)
}

/**
 * 处理页面 onResume 事件 主要做了24小时制的判断
 */
@Composable
private fun OnResumeEffect(
    context: Context,
    coordinator: HomeCoordinator
) {
    var is24Hour = remember {
        DateFormat.is24HourFormat(context)
    }

    LifecycleResumeEffect(coordinator) {
        coordinator.observeCategoryAndNotesList()
        val is24HourNew = DateFormat.is24HourFormat(context)
        if (is24Hour != is24HourNew) {
            is24Hour = is24HourNew
            coordinator.handleHomeAction(HomeNoteListAction.OnTimeFormatChanged(is24HourNew))
        }
        onPauseOrDispose {
            coordinator.stopObservingCategoryAndNotesList()
        }
    }
}
