package com.tcl.ai.note.home

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.startAIHelpWriting
import com.tct.theme.core.designsystem.component.TclTextButton
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            NoteTclTheme {
//                Scaffold(
//                    modifier = Modifier
//                        .fillMaxSize(),
//                    // 添加全局背景
//                ) { innerPadding ->
                Box(modifier = Modifier.fillMaxSize(),contentAlignment = Alignment.Center) {
                    Column {
                        Text("首页")
                        TclTextButton(onClick = {
                            startAIHelpWriting(0)
                        }){
                            Text(text = "AI 按钮")
                        }
                    }

                }
//                }
            }
        }
    }
}

