<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="com.tct.smart.aota.permission.CHECK_UPDATE" />
    <application>

        <activity
            android:name=".view.MainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
            android:exported="true"
            android:theme="@style/Theme.Note"
            android:windowSoftInputMode="adjustResize"
            tools:ignore="DiscouragedApi">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".view.EditActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
            android:exported="false"
            android:theme="@style/Theme.Note"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize">
        </activity>
        <activity
            android:name="com.tcl.ai.note.home.HomeActivity"
            android:exported="false"
            android:theme="@style/Theme.Note"/>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>