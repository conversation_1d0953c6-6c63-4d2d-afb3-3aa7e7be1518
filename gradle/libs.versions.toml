[versions]
adaptive = "1.1.0-alpha02"
agp = "8.10.1"
airbnbLottieCompose = "5.0.3"
autosign = "1.0.7"
autosignVersion = "1.0.8"
coilCompose = "3.2.0"
converterGson = "2.9.0"
datastorePreferences = "1.1.1"
digitalInkRecognition = "18.1.0"
fragment = "1.8.3"
graphicsCore = "1.0.0-alpha05"
kotlin = "2.1.0"
coreKtx = "1.10.1"
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.5.1"
kotlinSerialization = "2.0.20"
kotlinxCoroutinesPlayServices = "1.7.3"
kotlinxSerializationCore = "1.8.0"
kotlinxSerializationProtobuf = "1.8.0"
lifecycleRuntimeKtx = "2.6.1"
activityCompose = "1.8.0"
composeBom = "2025.03.00"
lifecycleService = "2.6.1"
lifecycleViewmodelKtx = "2.6.1"
lifecycleViewmodelCompose = "2.8.5"
okhttpVersion = "4.12.0"
retrofit = "2.11.0"
room = "2.6.1"
startup = "1.2.0"
tclComponentFrm = "35.0.0.003"
clientSdkEmbedded = "1.41.1"
#TCL UI 9.0
tcluiCompose = "1.0.5"
translate = "17.0.1"
appcompat = "1.4.0"
hilt = "2.56.2"
hiltNavigationCompose = "1.2.0"
navigationCompose = "2.8.0"
constraintlayoutComposeAndroid = "1.1.0"
constraintlayout = "2.2.0"
ksp = "2.1.0-1.0.29"
composeMaterial3 = "1.3.1"
utilcodex = "1.31.1"
workRuntimeKtx = "2.9.0"
gson = "2.10.1"
# tct
tct_bi_commonSDK = "3.4"
tct_ttvsBase = "1.2.7"
tct_bi_sdkTTVS = "2.0"
tct_ff_componentHttp = "1.2.3"
compose = "1.7.8"
material = "1.12.0"
compileSdk = "35"
minSdk = "34"
targetSdk = "35"
uiAndroid = "1.6.6"
foundationAndroid = "1.6.6"
kotlinxSerializationJson = "1.8.0"
activityKtx = "1.8.0"
### Ai Components
aiCommon = "1.1.1"
aiTranslate = "1.1.1"
aiSpeech = "1.1.2"
aiAssistant = "1.1.2"
jetbrainsMarkdown = "0.7.3"
ksoup = "0.5.0"

mlkitFaceDetection = "16.1.7"
opencv = "4.11.0"

[libraries]
airbnb-lottie-compose = { module = "com.airbnb.android:lottie-compose", version.ref = "airbnbLottieCompose" }
androidx-adaptive = { module = "androidx.compose.material3.adaptive:adaptive", version.ref = "adaptive" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastorePreferences" }
androidx-fragment = { module = "androidx.fragment:fragment", version.ref = "fragment" }
androidx-graphics-core = { module = "androidx.graphics:graphics-core", version.ref = "graphicsCore" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }
androidx-startup = { module = "androidx.startup:startup-runtime", version.ref = "startup" }
autosign = { module = "com.tct.sign.plugin:autosign", version.ref = "autosignVersion" }
coil-compose = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coilCompose" }
coil-network-okhttp = { module = "io.coil-kt.coil3:coil-network-okhttp", version.ref = "coilCompose" }
coil-gif = { module = "io.coil-kt.coil3:coil-gif", version.ref = "coilCompose" }
converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "converterGson" }
digital-ink-recognition = { module = "com.google.mlkit:digital-ink-recognition", version.ref = "digitalInkRecognition" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
compose-material3 = { group = "androidx.compose.material3", name = "material3", version.ref = "composeMaterial3" }
androidx-lifecycle-service = { group = "androidx.lifecycle", name = "lifecycle-service", version.ref = "lifecycleService" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycleViewmodelCompose" }
kotlin-serialization = { module = "org.jetbrains.kotlin:kotlin-serialization", version.ref = "kotlinSerialization" }
kotlinx-coroutines-play-services = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-play-services", version.ref = "kotlinxCoroutinesPlayServices" }
kotlinx-serialization-protobuf = { module = "org.jetbrains.kotlinx:kotlinx-serialization-protobuf", version.ref = "kotlinxSerializationProtobuf" }
kotlinx-serialization-core = { module = "org.jetbrains.kotlinx:kotlinx-serialization-core", version.ref = "kotlinxSerializationCore" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
okhttp3-okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttpVersion" }
tcl-componentfrm = { group = "com.tcl.component.sdk", name = "tcl-componentfrm-pubapi", version.ref = "tclComponentFrm" }
client-sdk-embedded = { group = "com.microsoft.cognitiveservices.speech", name = "client-sdk-embedded", version.ref = "clientSdkEmbedded" }
tclui-compose = { module = "com.tct.theme.core.designsystem:tclui-compose", version.ref = "tcluiCompose" }
tclui-compose-icons = { module = "com.tct.theme.core.icons:tclui-compose", version.ref = "tcluiCompose" }
tclui-compose-lint = { module = "com.tct.theme.core.designsystem:tclui-compose-lint", version.ref = "tcluiCompose" }
translate = { group = "com.google.mlkit", name = "translate", version.ref = "translate" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
dagger-hilt = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
dagger-hilt-compiler = { group = "com.google.dagger", name = "hilt-android-compiler", version.ref = "hilt" }
androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hiltNavigationCompose" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigationCompose" }
androidx-constraintlayout-compose-android = { group = "androidx.constraintlayout", name = "constraintlayout-compose-android", version.ref = "constraintlayoutComposeAndroid" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
jetbrains-markdown = { module = "org.jetbrains:markdown", version.ref = "jetbrainsMarkdown" }
ksoup-html = { module = "com.mohamedrejeb.ksoup:ksoup-html", version.ref = "ksoup" }
ksoup-entities = { module = "com.mohamedrejeb.ksoup:ksoup-entities", version.ref = "ksoup" }

compose-ui = { group = "androidx.compose.ui", name = "ui", version.ref = "compose" }
compose-material = { group = "androidx.compose.material", name = "material", version.ref = "compose" }
compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling", version.ref = "compose" }
compose-foundation = { group = "androidx.compose.foundation", name = "foundation", version.ref = "compose" }
androidx-work-runtime-ktx = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "workRuntimeKtx" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }

# tct
tct-commonSDK = { group = "com.tcl.bi.common", name = "sdk", version.ref = "tct_bi_commonSDK" }
tct-sdkTTVS = { group = "com.tcl.bi.sdk.ttvs", name = "ttvs", version.ref = "tct_bi_sdkTTVS" }
tct-ttvsBase = { group = "com.tcl.ttvs", name = "base", version.ref = "tct_ttvsBase" }
tct-ffComponentHttp = { group = "com.tcl.ff.component", name = "http", version.ref = "tct_ff_componentHttp" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-ui-android = { group = "androidx.compose.ui", name = "ui-android", version.ref = "uiAndroid" }
androidx-foundation-android = { group = "androidx.compose.foundation", name = "foundation-android", version.ref = "foundationAndroid" }
androidx-activity-ktx = { group = "androidx.activity", name = "activity-ktx", version.ref = "activityKtx" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }

mlkit-faceDetection = { group = "com.google.mlkit", name = "face-detection", version.ref = "mlkitFaceDetection" }
opencv = { group = "org.opencv", name = "opencv", version.ref = "opencv"}

### Ai Components
ai-common = { group = "com.tcl.ai.sdk", name = "ai-common", version.ref = "aiCommon" }
utilcodex = { module = "com.blankj:utilcodex", version.ref = "utilcodex" }
#ai-speech = { group = "com.tcl.ai.sdk", name = "ai-speech", version.ref = "aiSpeech" }
#ai-translate = { group = "com.tcl.ai.sdk", name = "ai-translate", version.ref = "aiTranslate" }
ai-assistant = { group = "com.tcl.ai.sdk", name = "ai-assistant", version.ref = "aiAssistant" }


[bundles]
tctBI = ["tct-commonSDK", "tct-sdkTTVS", "tct-ttvsBase"]


[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
google-dagger-hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
google-devtools-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
android-library = { id = "com.android.library", version.ref = "agp" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
