<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="28dp"
    android:height="28dp"
    android:viewportWidth="28"
    android:viewportHeight="28">
  <path
      android:pathData="M12.152,13.469L20.152,13.469Q20.226,13.469 20.298,13.483Q20.371,13.498 20.439,13.526Q20.507,13.554 20.569,13.595Q20.63,13.636 20.682,13.688Q20.734,13.741 20.775,13.802Q20.816,13.863 20.845,13.932Q20.873,14 20.887,14.072Q20.902,14.145 20.902,14.219Q20.902,14.293 20.887,14.365Q20.873,14.438 20.845,14.506Q20.816,14.574 20.775,14.635Q20.734,14.697 20.682,14.749Q20.63,14.801 20.569,14.842Q20.507,14.883 20.439,14.912Q20.371,14.94 20.298,14.954Q20.226,14.969 20.152,14.969L12.152,14.969Q12.078,14.969 12.006,14.954Q11.933,14.94 11.865,14.912Q11.797,14.883 11.735,14.842Q11.674,14.801 11.622,14.749Q11.569,14.697 11.528,14.635Q11.487,14.574 11.459,14.506Q11.431,14.438 11.416,14.365Q11.402,14.293 11.402,14.219Q11.402,14.145 11.416,14.072Q11.431,14 11.459,13.932Q11.487,13.863 11.528,13.802Q11.569,13.741 11.622,13.688Q11.674,13.636 11.735,13.595Q11.797,13.554 11.865,13.526Q11.933,13.498 12.006,13.483Q12.078,13.469 12.152,13.469Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M12.152,18.137L20.152,18.137Q20.226,18.137 20.298,18.151Q20.371,18.166 20.439,18.194Q20.507,18.222 20.569,18.263Q20.63,18.304 20.682,18.356Q20.734,18.409 20.775,18.47Q20.816,18.531 20.845,18.6Q20.873,18.668 20.887,18.74Q20.902,18.813 20.902,18.887Q20.902,18.961 20.887,19.033Q20.873,19.105 20.845,19.174Q20.816,19.242 20.775,19.303Q20.734,19.365 20.682,19.417Q20.63,19.469 20.569,19.51Q20.507,19.551 20.439,19.58Q20.371,19.608 20.298,19.622Q20.226,19.637 20.152,19.637L12.152,19.637Q12.078,19.637 12.006,19.622Q11.933,19.608 11.865,19.58Q11.797,19.551 11.735,19.51Q11.674,19.469 11.622,19.417Q11.569,19.365 11.528,19.303Q11.487,19.242 11.459,19.174Q11.431,19.105 11.416,19.033Q11.402,18.961 11.402,18.887Q11.402,18.813 11.416,18.74Q11.431,18.668 11.459,18.6Q11.487,18.531 11.528,18.47Q11.569,18.409 11.622,18.356Q11.674,18.304 11.735,18.263Q11.797,18.222 11.865,18.194Q11.933,18.166 12.006,18.151Q12.078,18.137 12.152,18.137Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M12.152,8.803L20.152,8.803Q20.226,8.803 20.298,8.817Q20.371,8.832 20.439,8.86Q20.507,8.888 20.569,8.929Q20.63,8.97 20.682,9.022Q20.734,9.075 20.775,9.136Q20.816,9.197 20.845,9.266Q20.873,9.334 20.887,9.406Q20.902,9.479 20.902,9.553Q20.902,9.627 20.887,9.699Q20.873,9.772 20.845,9.84Q20.816,9.908 20.775,9.969Q20.734,10.031 20.682,10.083Q20.63,10.135 20.569,10.176Q20.507,10.217 20.439,10.246Q20.371,10.274 20.298,10.288Q20.226,10.303 20.152,10.303L12.152,10.303Q12.078,10.303 12.006,10.288Q11.933,10.274 11.865,10.246Q11.797,10.217 11.735,10.176Q11.674,10.135 11.622,10.083Q11.569,10.031 11.528,9.969Q11.487,9.908 11.459,9.84Q11.431,9.772 11.416,9.699Q11.402,9.627 11.402,9.553Q11.402,9.479 11.416,9.406Q11.431,9.334 11.459,9.266Q11.487,9.197 11.528,9.136Q11.569,9.075 11.622,9.022Q11.674,8.97 11.735,8.929Q11.797,8.888 11.865,8.86Q11.933,8.832 12.006,8.817Q12.078,8.803 12.152,8.803Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M9.392,7.844L9.392,11.26L8.832,11.26L8.832,8.518Q8.521,8.801 8.052,8.935L8.052,8.38Q8.277,8.322 8.545,8.179Q8.799,8.026 8.971,7.844L9.392,7.844Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"/>
  <path
      android:pathData="M7.752,8.146L7.752,9.332L8.135,9.223Q8.347,9.163 8.532,9.074L8.532,11.56L9.692,11.56L9.692,7.544L8.842,7.544L8.753,7.637Q8.611,7.787 8.396,7.918Q8.165,8.041 7.978,8.089L7.752,8.146ZM8.832,8.518Q8.697,8.641 8.532,8.736Q8.446,8.785 8.352,8.827Q8.211,8.889 8.052,8.935L8.052,8.38Q8.277,8.322 8.545,8.179Q8.799,8.026 8.971,7.844L9.392,7.844L9.392,11.26L8.832,11.26L8.832,8.518Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M9.048,12.285Q9.527,12.285 9.836,12.562Q10.141,12.839 10.141,13.277Q10.141,13.697 9.818,14.057Q9.638,14.25 9.135,14.587Q8.632,14.919 8.522,15.168L10.145,15.168L10.145,15.643L7.848,15.643Q7.848,15.136 8.176,14.767Q8.356,14.559 8.932,14.163Q9.232,13.955 9.384,13.794Q9.601,13.549 9.601,13.272Q9.601,13.005 9.458,12.871Q9.315,12.742 9.029,12.742Q8.725,12.742 8.572,12.949Q8.42,13.143 8.406,13.535L7.867,13.535Q7.876,12.982 8.19,12.645Q8.517,12.285 9.048,12.285Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"/>
  <path
      android:pathData="M7.572,13.825L8.686,13.825L8.695,13.545Q8.706,13.248 8.8,13.128L8.803,13.124L8.806,13.121Q8.871,13.031 9.029,13.031Q9.204,13.031 9.26,13.082Q9.312,13.13 9.312,13.272Q9.312,13.438 9.171,13.598Q9.039,13.737 8.768,13.924Q8.161,14.342 7.959,14.575Q7.559,15.026 7.559,15.643L7.559,15.933L10.435,15.933L10.435,14.879L9.22,14.879Q9.226,14.875 9.233,14.87Q9.263,14.849 9.296,14.827Q9.827,14.472 10.033,14.25Q10.43,13.808 10.43,13.277Q10.43,12.711 10.029,12.346Q9.638,11.996 9.048,11.996Q8.389,11.996 7.978,12.448Q7.588,12.866 7.577,13.53L7.572,13.825ZM8.932,14.163Q9.232,13.955 9.384,13.794Q9.601,13.549 9.601,13.272Q9.601,13.005 9.458,12.871Q9.315,12.742 9.029,12.742Q8.725,12.742 8.572,12.949Q8.42,13.143 8.406,13.535L7.867,13.535Q7.869,13.382 7.895,13.246Q7.962,12.889 8.19,12.645Q8.517,12.285 9.048,12.285Q9.527,12.285 9.836,12.562Q10.141,12.839 10.141,13.277Q10.141,13.697 9.818,14.057Q9.638,14.25 9.135,14.587Q8.901,14.742 8.751,14.879Q8.581,15.035 8.522,15.168L10.145,15.168L10.145,15.643L7.848,15.643Q7.848,15.493 7.877,15.354Q7.945,15.026 8.176,14.767Q8.356,14.559 8.932,14.163Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M9.117,17.123Q9.64,17.123 9.949,17.37Q10.258,17.622 10.258,18.059Q10.258,18.616 9.692,18.801Q10.001,18.896 10.153,19.077Q10.325,19.267 10.325,19.571Q10.325,20.041 9.997,20.341Q9.65,20.65 9.098,20.65Q8.571,20.65 8.247,20.379Q7.891,20.08 7.848,19.504L8.414,19.504Q8.428,19.837 8.623,20.013Q8.804,20.179 9.093,20.179Q9.412,20.179 9.602,19.999Q9.768,19.832 9.768,19.595Q9.768,19.305 9.593,19.167Q9.426,19.029 9.093,19.029L8.856,19.029L8.856,18.611L9.093,18.611Q9.393,18.611 9.55,18.482Q9.702,18.354 9.702,18.102Q9.702,17.855 9.564,17.727Q9.417,17.598 9.122,17.598Q8.823,17.598 8.661,17.746Q8.49,17.893 8.461,18.202L7.915,18.202Q7.957,17.684 8.295,17.403Q8.609,17.123 9.117,17.123Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"/>
  <path
      android:pathData="M7.591,18.5L8.558,18.5L8.558,19.206L7.527,19.206L7.551,19.526Q7.603,20.227 8.056,20.607Q8.462,20.948 9.098,20.948Q9.763,20.948 10.198,20.561Q10.623,20.173 10.623,19.571Q10.623,19.155 10.378,18.881Q10.337,18.832 10.289,18.789Q10.556,18.514 10.556,18.059Q10.556,17.481 10.135,17.137Q9.745,16.825 9.117,16.825Q8.498,16.825 8.1,17.178Q7.67,17.538 7.618,18.177L7.591,18.5ZM9.702,18.102Q9.702,17.855 9.564,17.727Q9.417,17.598 9.122,17.598Q8.823,17.598 8.661,17.746Q8.49,17.893 8.461,18.202L7.915,18.202Q7.928,18.042 7.969,17.904Q8.062,17.597 8.295,17.403Q8.609,17.123 9.117,17.123Q9.64,17.123 9.949,17.37Q10.258,17.622 10.258,18.059Q10.258,18.412 10.031,18.615Q9.9,18.733 9.692,18.801Q9.906,18.867 10.044,18.973Q10.106,19.021 10.153,19.077Q10.325,19.267 10.325,19.571Q10.325,20.041 9.997,20.341Q9.65,20.65 9.098,20.65Q8.571,20.65 8.247,20.379Q7.99,20.163 7.896,19.803Q7.86,19.664 7.848,19.504L8.414,19.504Q8.428,19.837 8.623,20.013Q8.804,20.179 9.093,20.179Q9.412,20.179 9.602,19.999Q9.768,19.832 9.768,19.595Q9.768,19.305 9.593,19.167Q9.426,19.029 9.093,19.029L8.856,19.029L8.856,18.611L9.093,18.611Q9.393,18.611 9.55,18.482Q9.702,18.354 9.702,18.102ZM9.093,18.313L8.751,18.313L8.758,18.229Q8.776,18.04 8.855,17.972L8.859,17.969L8.862,17.966Q8.938,17.897 9.122,17.897Q9.299,17.897 9.364,17.948L9.365,17.949Q9.404,17.99 9.404,18.102Q9.404,18.216 9.361,18.252Q9.286,18.313 9.093,18.313ZM9.47,19.595Q9.47,19.707 9.394,19.785Q9.382,19.796 9.369,19.806Q9.268,19.881 9.093,19.881Q8.92,19.881 8.823,19.792Q8.721,19.699 8.712,19.492L8.705,19.327L9.093,19.327Q9.319,19.327 9.402,19.396L9.405,19.399L9.409,19.402Q9.47,19.45 9.47,19.595Z"
      android:fillColor="#000000"
      android:fillAlpha="0.85"
      android:fillType="evenOdd"/>
</vector>
