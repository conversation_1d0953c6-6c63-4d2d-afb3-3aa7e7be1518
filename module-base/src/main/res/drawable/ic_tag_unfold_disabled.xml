<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="16dp"
    android:height="16dp"
    android:viewportWidth="16"
    android:viewportHeight="16">
  <group>
    <clip-path
        android:pathData="M0,16l16,-0l0,-16l-16,-0z"/>
    <path
        android:pathData="M8,6.067L4,10.067L4.368,10.434C4.68,10.747 5.187,10.747 5.499,10.434L8,7.933L10.501,10.434C10.813,10.747 11.32,10.747 11.632,10.434L12,10.067L8,6.067Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="4"
            android:startY="5.71"
            android:endX="12"
            android:endY="10.747"
            android:type="linear">
          <item android:offset="0" android:color="#FFA0A0B0"/> <!-- 浅灰蓝色 -->
          <item android:offset="1" android:color="#FFC0C0D0"/> <!-- 中灰蓝色 -->
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
