<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="28dp"
    android:height="28dp"
    android:viewportWidth="28"
    android:viewportHeight="28">
  <group>
    <clip-path
        android:pathData="M5.75,6.25h16v16h-16z"/>
    <path
        android:pathData="M7.474,13.307Q7.083,13.698 7.083,14.25Q7.083,14.802 7.474,15.193Q7.864,15.583 8.417,15.583Q8.969,15.583 9.359,15.193Q9.75,14.802 9.75,14.25Q9.75,13.698 9.359,13.307Q8.969,12.917 8.417,12.917Q7.864,12.917 7.474,13.307Z"
        android:fillColor="#000000"
        android:fillAlpha="0.85"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M7.474,8.644Q7.083,9.034 7.083,9.587Q7.083,10.139 7.474,10.529Q7.864,10.92 8.417,10.92Q8.969,10.92 9.359,10.529Q9.75,10.139 9.75,9.587Q9.75,9.034 9.359,8.644Q8.969,8.253 8.417,8.253Q7.864,8.253 7.474,8.644Z"
        android:fillColor="#000000"
        android:fillAlpha="0.85"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M7.474,17.974Q7.083,18.364 7.083,18.917Q7.083,19.469 7.474,19.859Q7.864,20.25 8.417,20.25Q8.969,20.25 9.359,19.859Q9.75,19.469 9.75,18.917Q9.75,18.364 9.359,17.974Q8.969,17.583 8.417,17.583Q7.864,17.583 7.474,17.974Z"
        android:fillColor="#000000"
        android:fillAlpha="0.85"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M12.417,13.5L20.417,13.5Q20.491,13.5 20.563,13.514Q20.636,13.529 20.704,13.557Q20.772,13.585 20.834,13.626Q20.895,13.667 20.947,13.72Q21,13.772 21.041,13.833Q21.082,13.895 21.11,13.963Q21.138,14.031 21.153,14.104Q21.167,14.176 21.167,14.25Q21.167,14.324 21.153,14.396Q21.138,14.469 21.11,14.537Q21.082,14.605 21.041,14.667Q21,14.728 20.947,14.78Q20.895,14.833 20.834,14.874Q20.772,14.915 20.704,14.943Q20.636,14.971 20.563,14.986Q20.491,15 20.417,15L12.417,15Q12.343,15 12.271,14.986Q12.198,14.971 12.13,14.943Q12.062,14.915 12,14.874Q11.939,14.833 11.887,14.78Q11.834,14.728 11.793,14.667Q11.752,14.605 11.724,14.537Q11.696,14.469 11.681,14.396Q11.667,14.324 11.667,14.25Q11.667,14.176 11.681,14.104Q11.696,14.031 11.724,13.963Q11.752,13.895 11.793,13.833Q11.834,13.772 11.887,13.72Q11.939,13.667 12,13.626Q12.062,13.585 12.13,13.557Q12.198,13.529 12.271,13.514Q12.343,13.5 12.417,13.5Z"
        android:fillColor="#000000"
        android:fillAlpha="0.85"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M12.417,18.167L20.417,18.167Q20.491,18.167 20.563,18.181Q20.636,18.195 20.704,18.224Q20.772,18.252 20.834,18.293Q20.895,18.334 20.947,18.386Q21,18.438 21.041,18.5Q21.082,18.561 21.11,18.629Q21.138,18.698 21.153,18.77Q21.167,18.843 21.167,18.917Q21.167,18.99 21.153,19.063Q21.138,19.135 21.11,19.204Q21.082,19.272 21.041,19.333Q21,19.395 20.947,19.447Q20.895,19.499 20.834,19.54Q20.772,19.581 20.704,19.609Q20.636,19.638 20.563,19.652Q20.491,19.667 20.417,19.667L12.417,19.667Q12.343,19.667 12.271,19.652Q12.198,19.638 12.13,19.609Q12.062,19.581 12,19.54Q11.939,19.499 11.887,19.447Q11.834,19.395 11.793,19.333Q11.752,19.272 11.724,19.204Q11.696,19.135 11.681,19.063Q11.667,18.99 11.667,18.917Q11.667,18.843 11.681,18.77Q11.696,18.698 11.724,18.629Q11.752,18.561 11.793,18.5Q11.834,18.438 11.887,18.386Q11.939,18.334 12,18.293Q12.062,18.252 12.13,18.224Q12.198,18.195 12.271,18.181Q12.343,18.167 12.417,18.167Z"
        android:fillColor="#000000"
        android:fillAlpha="0.85"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M12.417,8.833L20.417,8.833Q20.491,8.833 20.563,8.848Q20.636,8.862 20.704,8.891Q20.772,8.919 20.834,8.96Q20.895,9.001 20.947,9.053Q21,9.105 21.041,9.167Q21.082,9.228 21.11,9.296Q21.138,9.365 21.153,9.437Q21.167,9.51 21.167,9.583Q21.167,9.657 21.153,9.73Q21.138,9.802 21.11,9.871Q21.082,9.939 21.041,10Q21,10.062 20.947,10.114Q20.895,10.166 20.834,10.207Q20.772,10.248 20.704,10.276Q20.636,10.305 20.563,10.319Q20.491,10.333 20.417,10.333L12.417,10.333Q12.343,10.333 12.271,10.319Q12.198,10.305 12.13,10.276Q12.062,10.248 12,10.207Q11.939,10.166 11.887,10.114Q11.834,10.062 11.793,10Q11.752,9.939 11.724,9.871Q11.696,9.802 11.681,9.73Q11.667,9.657 11.667,9.583Q11.667,9.51 11.681,9.437Q11.696,9.365 11.724,9.296Q11.752,9.228 11.793,9.167Q11.834,9.105 11.887,9.053Q11.939,9.001 12,8.96Q12.062,8.919 12.13,8.891Q12.198,8.862 12.271,8.848Q12.343,8.833 12.417,8.833Z"
        android:fillColor="#000000"
        android:fillAlpha="0.85"
        android:fillType="evenOdd"/>
  </group>
</vector>
