<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="16dp"
    android:height="16dp"
    android:viewportWidth="16"
    android:viewportHeight="16">
  <group>
    <clip-path
        android:pathData="M0,16l16,-0l0,-16l-16,-0z"/>
    <path
        android:pathData="M8,11L4,7L4.368,6.632C4.68,6.32 5.187,6.32 5.499,6.632L8,9.133L10.501,6.632C10.813,6.32 11.32,6.32 11.632,6.632L12,7L8,11Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="4"
            android:startY="5.963"
            android:endX="12"
            android:endY="11"
            android:type="linear">
          <item android:offset="0" android:color="#FFA0A0B0"/> <!-- 浅灰蓝色 -->
          <item android:offset="1" android:color="#FFC0C0D0"/> <!-- 中灰蓝色 -->
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
