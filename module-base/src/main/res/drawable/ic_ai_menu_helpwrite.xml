<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="45dp" android:viewportHeight="45" android:viewportWidth="55" android:width="55dp">
      
    <path android:fillColor="#A677FE" android:pathData="M49.719,20.282C50.625,22.719 52.532,24.641 54.95,25.555C52.532,26.469 50.625,28.391 49.719,30.828C48.813,28.391 46.905,26.469 44.488,25.555C46.905,24.641 48.813,22.719 49.719,20.282Z"/>
      
    <path android:pathData="M18.669,44.998H53.972V39.067H24.553L18.669,44.998ZM1.017,44.996L0.953,33.133L32.16,1.731C34.458,-0.581 38.181,-0.58 40.477,1.735L44.344,5.633L48.089,9.407L44.344,13.182L12.786,44.997L1.017,44.996ZM6.284,39.658L10.592,39.659L40.6,9.408L36.733,5.51C36.456,5.231 36.179,5.231 35.901,5.51L6.261,35.335L6.284,39.658Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="54.953" android:endY="33.465" android:startX="0.036" android:startY="33.465" android:type="linear">
                        
                <item android:color="#FF1282FD" android:offset="0"/>
                        
                <item android:color="#FFB676FF" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
    
</vector>
