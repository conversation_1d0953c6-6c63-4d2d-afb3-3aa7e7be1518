<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="102dp" android:viewportHeight="102" android:viewportWidth="102" android:width="102dp">
      
    <path android:fillAlpha="0.3" android:pathData="M9.34,50.35L70.23,27.74C71.78,27.16 73.51,27.95 74.08,29.5C74.33,30.18 74.33,30.92 74.08,31.59L51.47,92.48C50.89,94.03 49.17,94.82 47.61,94.25C46.82,93.95 46.2,93.35 45.88,92.57L37.75,72.63C37.36,71.66 37.5,70.55 38.12,69.71L52.18,50.69C52.42,50.36 52.35,49.89 52.02,49.64C51.76,49.45 51.39,49.45 51.13,49.64L32.11,63.7C31.27,64.32 30.16,64.46 29.19,64.07L9.25,55.94C7.72,55.32 6.98,53.57 7.61,52.03C7.92,51.25 8.55,50.64 9.34,50.35ZM44.54,10.61C48.34,12.36 52.69,12.46 56.55,10.92L57.27,10.61C55.52,14.41 55.42,18.75 56.96,22.62L57.27,23.34C53.47,21.59 49.13,21.48 45.26,23.03L44.54,23.34C46.4,19.3 46.4,14.65 44.54,10.61Z" android:strokeAlpha="0.3">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="78.48" android:endY="8.49" android:startX="22.27" android:startY="56.22" android:type="linear">
                        
                <item android:color="#FF0088FF" android:offset="0"/>
                        
                <item android:color="#FF0000FF" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
    
</vector>
