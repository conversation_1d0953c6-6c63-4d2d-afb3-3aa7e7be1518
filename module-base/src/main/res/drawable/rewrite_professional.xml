<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">

  <path
      android:pathData="M9.74,3.994L8,3.994L8,4L7,4C5.895,4 5,4.895 5,6L5,20L12,20L12,18.5L6.5,18.5L6.5,6Q6.5,5.5 7,5.5L8,5.5L8,6.994L16,6.994L16,5.5L17.5,5.5L17.5,12L19,12L19,4L16,4L16,3.994L14.28,3.994Q14.06,3.294 13.43,2.844Q12.8,2.394 12,2.394Q11.24,2.394 10.6,2.844Q9.96,3.294 9.74,3.994ZM12.109,4Q12.056,3.994 12,3.994Q11.944,3.994 11.891,4L11.892,4Q11.622,4.031 11.43,4.224Q11.2,4.454 11.2,4.794Q11.2,5.134 11.43,5.364Q11.66,5.594 12,5.594Q12.34,5.594 12.57,5.364Q12.8,5.134 12.8,4.794Q12.8,4.454 12.57,4.224Q12.377,4.031 12.108,4L12.109,4Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="19"
          android:startY="11.197"
          android:endX="5"
          android:endY="11.197"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M8,8.947h8v1.5h-8z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16"
          android:startY="9.697"
          android:endX="8"
          android:endY="9.697"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M8,11.947h4v1.5h-4z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="12"
          android:startY="12.697"
          android:endX="8"
          android:endY="12.697"
          android:type="linear">
        <item android:offset="0" android:color="#FFC95EFF"/>
        <item android:offset="1" android:color="#FF436BF1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M14.925,17.937L14.924,17.938L15.475,18.489L19.482,14.482L19.049,14.049C18.983,13.984 18.878,13.984 18.813,14.049L15.475,17.386L13.668,15.579C13.603,15.514 13.498,15.514 13.433,15.579L13,16.012L14.925,17.937Z"
      android:fillColor="#4560E6"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M15.279,18.29L15.277,18.291L14.924,17.938L15.277,17.584L15.828,18.135L15.475,18.489L15.121,18.135L19.128,14.128L19.482,14.482L19.128,14.835L18.695,14.402Q18.793,14.5 18.931,14.5Q19.069,14.5 19.166,14.402L15.475,18.093L13.315,15.933Q13.412,16.03 13.55,16.03Q13.689,16.03 13.786,15.933L13.354,16.365L13,16.012L13.354,15.658L15.632,17.937L15.279,18.29ZM14.571,17.583L14.925,17.937L14.571,18.29L12.293,16.012L13.079,15.225Q13.274,15.03 13.55,15.03Q13.827,15.03 14.022,15.225L15.829,17.033L15.475,17.386L15.122,17.033L18.459,13.695Q18.655,13.5 18.931,13.5Q19.207,13.5 19.402,13.695L20.189,14.482L15.475,19.196L14.217,17.938L14.571,17.583Z"
      android:fillColor="#7366F6"/>
</vector>
