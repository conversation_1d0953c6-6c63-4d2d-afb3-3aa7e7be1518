<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="102dp" android:viewportHeight="102" android:viewportWidth="102" android:width="102dp">
      
    <path android:pathData="M9.34,50.41L70.23,27.79C71.78,27.21 73.51,28 74.08,29.56C74.33,30.23 74.33,30.97 74.08,31.65L51.47,92.53C50.89,94.09 49.17,94.88 47.61,94.3C46.82,94.01 46.2,93.4 45.88,92.62L37.75,72.68C37.36,71.71 37.5,70.61 38.12,69.76L52.18,50.75C52.42,50.41 52.35,49.94 52.02,49.7C51.76,49.5 51.39,49.5 51.13,49.7L32.11,63.75C31.27,64.38 30.16,64.51 29.19,64.12L9.25,56C7.72,55.37 6.98,53.62 7.61,52.09C7.92,51.31 8.55,50.7 9.34,50.41ZM44.54,10.66C48.34,12.41 52.69,12.51 56.55,10.97L57.27,10.66C55.52,14.46 55.42,18.81 56.96,22.67L57.27,23.39C53.47,21.64 49.13,21.54 45.26,23.08L44.54,23.39C46.4,19.35 46.4,14.7 44.54,10.66Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="78.48" android:endY="8.54" android:startX="22.27" android:startY="56.27" android:type="linear">
                        
                <item android:color="#FF0088FF" android:offset="0"/>
                        
                <item android:color="#FF0000FF" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
    
</vector>
