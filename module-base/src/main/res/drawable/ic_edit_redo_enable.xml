<!--<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="20dp"
    android:height="20dp"
    android:viewportWidth="20"
    android:viewportHeight="20">
    <group>
        <clip-path
            android:pathData="M0,0C0,0 0,0 0,0L20,0C20,0 20,0 20,0L20,20C20,20 20,20 20,20L0,20C0,20 0,20 0,20Z"/>
        <path
            android:pathData="M13.838,5.504C14.098,5.244 14.52,5.244 14.781,5.504L17.131,7.854L17.193,7.924C17.398,8.188 17.376,8.566 17.138,8.804L14.788,11.154L14.718,11.217C14.454,11.422 14.076,11.4 13.838,11.161C13.578,10.901 13.578,10.479 13.838,10.219L15.057,8.999L7.012,8.999C5.539,8.999 4.345,10.193 4.345,11.666L4.345,14.167C4.345,14.535 4.046,14.833 3.678,14.833C3.31,14.833 3.012,14.535 3.012,14.167L3.012,11.666C3.012,9.457 4.802,7.666 7.012,7.666L15.057,7.666L13.838,6.447C13.601,6.21 13.58,5.84 13.773,5.579L13.838,5.504Z"
            android:strokeAlpha="0.9"
            android:fillColor="#000000"
            android:fillAlpha="0.3"/>
    </group>
</vector>-->


<vector xmlns:android="http://schemas.android.com/apk/res/android"
android:width="24dp"
android:height="24dp"
android:viewportWidth="24"
android:viewportHeight="24">
<group>
    <clip-path
        android:pathData="M0,0h24v24h-24z"/>
    <path
        android:pathData="M24,0C24,0 24,0 24,0L0,0C0,0 0,0 0,0L0,24C0,24 0,24 0,24L24,24C24,24 24,24 24,24Z"
        android:fillColor="#000000"
        android:fillAlpha="0"/>
    <path
        android:pathData="M10.333,17.667L17.833,17.667Q17.912,17.667 17.989,17.682Q18.067,17.697 18.139,17.727Q18.212,17.758 18.278,17.801Q18.343,17.845 18.399,17.901Q18.455,17.957 18.499,18.022Q18.542,18.088 18.572,18.16Q18.603,18.233 18.618,18.31Q18.633,18.388 18.633,18.467Q18.633,18.545 18.618,18.623Q18.603,18.7 18.572,18.773Q18.542,18.845 18.499,18.911Q18.455,18.976 18.399,19.032Q18.343,19.088 18.278,19.132Q18.212,19.175 18.139,19.206Q18.067,19.236 17.989,19.251Q17.912,19.267 17.833,19.267L10.333,19.267Q7.793,19.267 5.996,17.47Q4.2,15.674 4.2,13.133Q4.2,10.593 5.996,8.796Q7.793,7 10.333,7L17.833,7Q17.912,7 17.989,7.015Q18.067,7.031 18.139,7.061Q18.212,7.091 18.278,7.135Q18.343,7.178 18.399,7.234Q18.455,7.29 18.499,7.355Q18.542,7.421 18.572,7.494Q18.603,7.566 18.618,7.644Q18.633,7.721 18.633,7.8Q18.633,7.879 18.618,7.956Q18.603,8.033 18.572,8.106Q18.542,8.179 18.499,8.244Q18.455,8.31 18.399,8.366Q18.343,8.421 18.278,8.465Q18.212,8.509 18.139,8.539Q18.067,8.569 17.989,8.584Q17.912,8.6 17.833,8.6L10.333,8.6Q8.456,8.6 7.128,9.928Q5.8,11.255 5.8,13.133Q5.8,15.011 7.128,16.339Q8.456,17.667 10.333,17.667Z"
        android:fillColor="#000000"
        android:fillAlpha="0.3"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M19.212,8.719L16.266,11.665Q16.21,11.721 16.145,11.765Q16.079,11.809 16.006,11.839Q15.934,11.869 15.856,11.884Q15.779,11.9 15.7,11.9Q15.621,11.9 15.544,11.884Q15.467,11.869 15.394,11.839Q15.321,11.809 15.256,11.765Q15.19,11.721 15.134,11.665Q15.079,11.61 15.035,11.544Q14.991,11.479 14.961,11.406Q14.931,11.333 14.916,11.256Q14.9,11.178 14.9,11.1Q14.9,11.021 14.916,10.944Q14.931,10.866 14.961,10.794Q14.991,10.721 15.035,10.655Q15.079,10.59 15.134,10.534L17.869,7.8L15.134,5.066Q15.079,5.01 15.035,4.944Q14.991,4.879 14.961,4.806Q14.931,4.733 14.916,4.656Q14.9,4.579 14.9,4.5Q14.9,4.421 14.916,4.344Q14.931,4.267 14.961,4.194Q14.991,4.121 15.035,4.056Q15.079,3.99 15.134,3.934Q15.19,3.879 15.256,3.835Q15.321,3.791 15.394,3.761Q15.467,3.731 15.544,3.715Q15.621,3.7 15.7,3.7Q15.779,3.7 15.856,3.715Q15.934,3.731 16.006,3.761Q16.079,3.791 16.145,3.835Q16.21,3.879 16.266,3.934L19.212,6.881Q19.593,7.261 19.593,7.8Q19.593,8.338 19.212,8.719Z"
        android:fillColor="#000000"
        android:fillAlpha="0.3"
        android:fillType="evenOdd"/>
</group>
</vector>
