package com.tcl.ai.note.controller

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.provider.Settings
import android.widget.Toast
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.core.net.toUri
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.controller.AIAppServiceHelper.checkAppInstalled
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getSystemProperty

/**
 * 处理 TCL AI 应用服务相关的工具类
 */
object AIAppServiceHelper {
    
    /**
     * 检查 TCL AI 应用服务是否安装
     */
    fun checkAppInstalled(context: Context, packageName: String = "com.tcl.ai.app"): Bo<PERSON>an {
        return try {
            Logger.d("AIAppServiceHelper", "checkAppInstalled: $packageName")
            context.packageManager.getApplicationInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings(context: Context, packageName: String = "com.tcl.ai.app") {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = "package:$packageName".toUri()
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_SETTINGS).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } catch (e: Exception) {
            }
        }
    }
    
    /**
     * 启动应用
     */
    fun launchApp(context: Context, packageName: String = "com.tcl.ai.app") {
        try {
            val launchIntent = context.packageManager.getLaunchIntentForPackage(packageName)
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(launchIntent)

            } else {
                openAppSettings(context, packageName)
            }
        } catch (e: Exception) {
            openAppSettings(context, packageName)
        }
    }

}

/**
 * 检查 AI 服务是否启用
 * TCL AI App和 TCL AI Ability 都安装了才认为可用 否则隐藏AI功能
 */
val isAIServiceEnable: Boolean by lazy {
    val context = GlobalContext.instance
    val isInstallTClAI = checkAppInstalled(context, "com.tcl.ai.app")
    val isInstallAbility = checkAppInstalled(context, "com.tcl.ai.ability")
    Logger.d("AIAppServiceHelper", "isAIServiceEnable: $isInstallTClAI, $isInstallAbility")
     true
//    isInstallTClAI && isInstallAbility
}