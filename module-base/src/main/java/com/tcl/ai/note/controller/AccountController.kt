package com.tcl.ai.note.controller

import com.tcl.ai.bridge.connector.callback.LoginListenServiceCallback
import com.tcl.ai.bridge.connector.controller.impl.LoginListenServiceController
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.runIO
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

sealed class BottomRoute {
    data object Summary : BottomRoute()
    data object Polish : BottomRoute()
    data object HelpWrite : BottomRoute()
}

object AccountController {
    private const val TAG = "AccountController"
    private val context
        get() = GlobalContext.instance
    private var loginService: LoginListenServiceController? = null
    private var serviceConnected: Boolean = false

    // 添加登录状态流，用于观察登录状态变化
    val isLogoutFlow = MutableStateFlow(false)

    // 缓存登录信息，避免频繁调用服务
    private var isLogin = false
    private var loginToken = ""
    private var loginCountryCode = ""
    private var baseHostUrl = ""
    private var fileSaasUrl = ""

    private val loginStatusCallback = object : LoginListenServiceCallback {
        override fun onLoginStatusChanged(
            token: String,
            countryCode: String,
            loginStatus: Boolean
        ) {
            Logger.v(TAG, "onLoginStatusChanged $loginStatus")
            isLogoutFlow.value = !loginStatus

            // 登录状态变化，更新本地缓存的token以及登录状态
            isLogin = loginStatus
            baseHostUrl = loginService?.getBaseHost().orEmpty()
            loginToken = loginService?.getToken().orEmpty()
            loginCountryCode = loginService?.getCountryCode().orEmpty()
            fileSaasUrl = loginService?.getFilesaasBaseUrl().orEmpty()

            Logger.d(TAG, "onLoginStatusChanged isLogin: $isLogin, baseHostUrl: $baseHostUrl, loginToken: $loginToken, loginCountryCode: $loginCountryCode")
        }

        override fun onServiceConnected() {
            Logger.v(TAG, "onServiceConnected")
            serviceConnected = true

            // 服务连接成功后，立即更新登录状态和相关信息
            isLogin = loginService?.isLogin() ?: false
            baseHostUrl = loginService?.getBaseHost().orEmpty()
            loginToken = loginService?.getToken().orEmpty()
            loginCountryCode = loginService?.getCountryCode().orEmpty()
            fileSaasUrl = loginService?.getFilesaasBaseUrl().orEmpty()

            Logger.d(TAG, "onServiceConnected isLogin: $isLogin, baseHostUrl: $baseHostUrl, loginToken: $loginToken, loginCountryCode: $loginCountryCode")
        }

        override fun onServiceDisconnected() {
            Logger.v(TAG, "onServiceDisconnected")
            loginService = null
            serviceConnected = false

            // 服务断开连接，清空登录信息
            isLogin = false
            isLogoutFlow.value = true
            baseHostUrl = ""
            loginToken = ""
            loginCountryCode = ""
            fileSaasUrl = ""

            // 服务断开后尝试重新连接
            retryConnect()
        }
    }

    /**
     * 进入富文本页面会被 AudioToTextViewModel init初始化
     */
    fun connect() {
        synchronized(this) {
            if (loginService == null) {
                Logger.v(TAG, "try connect account service")
                loginService = LoginListenServiceController(context).apply {
                    connectService()
                    addCallback(loginStatusCallback)
                }
            }
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun retryConnect(retryCount: Int = 5, delayMs: Long = 1000) {
        Logger.d(TAG, "retryConnect")
        var currentRetryCount = 0
        var currentDelay = delayMs

        // 使用 GlobalScope 启动一个新的协程
        GlobalScope.launch(Dispatchers.IO) {
            while (!serviceConnected && currentRetryCount < retryCount) {
                connect()
                currentRetryCount++
                currentDelay *= 2  // 间隔时间递增

                // 延迟一定时间
                delay(currentDelay)
            }

            if (!serviceConnected) {
                Logger.e(TAG, "retryConnect failed after $currentRetryCount times.")
            }
        }
    }

    /**
     * 断开连接，登录会失效
     */
    private fun disconnect() {
        loginService?.apply {
            try {
                removeCallback(loginStatusCallback)
                Logger.v(TAG, "try disconnect account service")
                if (serviceConnected) {
                    release()
                }
            } catch (ex: Exception) {
                Logger.e(TAG, "release service error: ${ex.message}")
            }
        }
        loginService = null
        serviceConnected = false

        // 清空登录信息
        isLogin = false
        baseHostUrl = ""
        loginToken = ""
        loginCountryCode = ""
        fileSaasUrl = ""
    }

    /**
     * 在IO线程断开连接，登录会失效
     */
    suspend fun disconnectInIO() {
       runIO {
           disconnect()
       }
    }


    /**
     * 获取登录状态
     * 直接调用 loginService?.isLogin() 更准确一点
     * 有时候 serviceConnected 没有回调
     */
    suspend fun getLoginState(): Boolean {
        val startTime = System.currentTimeMillis()
        var result = false
        try {
            val max = 2// 重试次数 3次就行 不然等待时间太长
            for (i in 0..max) {
                Logger.v(TAG, "try to check login state: $i")
                result = try {
                    loginService?.isLogin() == true
                } catch (ex: Exception) {
                    Logger.v(TAG, "loop check login state error: ${ex.message}")
                    false
                }
                if (result) {
                    break
                }
                delay(200)
            }

            // 更新缓存的登录状态
            isLogin = result
        } catch (ex: Exception) {
            Logger.e(TAG, "check login state error: ${ex.message}")
        }

        val endTime = System.currentTimeMillis()
        val costTime = endTime - startTime

        // 记录方法总耗时
        Logger.d(TAG, "getLoginState total cost time: $costTime ms, result: $result")
        return result
    }

    /**
     * 获取国家代码，优先使用缓存的值
     */
    val countryCode
        get() = loginCountryCode.ifBlank {
            val countryCode = loginService?.getCountryCode().orEmpty()
            Logger.d(TAG, "countryCode is empty, try to get from service : $countryCode")
            countryCode
        }

    /**
     * 获取服务器地址，优先使用缓存的值
     * 如果缓存为空，则直接调用loginService?.getBaseHost()获取
     */
    val baseHost
        get() = baseHostUrl.ifBlank {
            val hostUrl = loginService?.getBaseHost().orEmpty()
            Logger.d(TAG, "baseHost or login info is empty, try to get from service : $hostUrl")
            hostUrl
        }
    /**
     * 获取token，优先使用缓存的值
     */
    val token
        get() = loginToken.ifBlank {
            val token = loginService?.getToken().orEmpty()
            //Logger.d(TAG, "token is empty, try to get from service : $token")
            token
        }

    /**
     * 获取服务连接状态
     */
    fun getServiceConnected(): Boolean {
        return serviceConnected
    }

    /**
     * 获取文件服务地址，优先使用缓存的值
     */
    val fileSaasBaseUrl
        get() = fileSaasUrl.ifBlank {
            val fileSaasUrl = loginService?.getFilesaasBaseUrl().orEmpty()
            Logger.d(TAG, "fileSaasBaseUrl is empty, try to get from service : $fileSaasUrl")
            fileSaasUrl
        }

    /**
     * 重新连接服务
     * 用于登录状态变化时刷新连接
     */
    fun reconnect() {
           synchronized(this) {
               disconnect()
               connect()
           }
        Logger.v(TAG, "reconnect cost time: Thread: ${Thread.currentThread().name}")
    }
}