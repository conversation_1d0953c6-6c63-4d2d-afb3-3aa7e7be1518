package com.tcl.ai.note.base

import android.os.Bundle
import android.os.PersistableBundle
import android.view.View
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.utils.Logger

/**
 * 基础 Activity 类，处理通用的生命周期事件
 */
open class BaseActivity : AppCompatActivity() {
    private val TAG = "BaseActivity"

    // 记录上一次的生命周期状态
    private var wasResumed = false
    // 分发状态栏和键盘变化
    private val onApplyWindowInsetsListenerSet = mutableSetOf<OnApplyWindowInsetsListener>()

    /**
     * 提供给子view，用来监听状态栏变化、键盘变化
     */
    fun addOnApplyWindowInsetsListener(onApplyWindowInsetsListener: OnApplyWindowInsetsListener): Unit {
        onApplyWindowInsetsListenerSet.add(onApplyWindowInsetsListener)
    }

    fun removeOnApplyWindowInsetsListener(onApplyWindowInsetsListener: OnApplyWindowInsetsListener): Unit {
        onApplyWindowInsetsListenerSet.remove(onApplyWindowInsetsListener)
    }

    /**
     * 当用户按下 recent 键（最近任务键）时调用
     * 这个方法会通知 AppActivityManager 用户离开了应用
     * 注意：在某些设备上这个方法可能不会被调用
     */
    override fun onUserLeaveHint() {
        super.onUserLeaveHint()
        Logger.d(TAG, "onUserLeaveHint called")
        AppActivityManager.onUserLeaveHint()
    }

    override fun onCreate(savedInstanceState: Bundle?, persistentState: PersistableBundle?) {
        Logger.d(TAG, "onCreate called")
        super.onCreate(savedInstanceState, persistentState)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        Logger.d(TAG, "onAttachedToWindow called")
        // 弹出键盘时，缩小布局。避免光标水滴穿透键盘
        ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { view, windowInsets ->
//            val imeHeight = windowInsets.getInsets(WindowInsetsCompat.Type.ime()).bottom
//            view.setPadding(view.paddingLeft, view.paddingTop, view.paddingRight, imeHeight)
            onApplyWindowInsetsListenerSet.toList().forEach { it.onApplyWindowInsets(view, windowInsets) }
            Logger.d(TAG, "onApplyWindowInsets called")
            return@setOnApplyWindowInsetsListener windowInsets
        }
    }

    override fun onStart() {
        Logger.d(TAG, "onStart called")
        super.onStart()
    }

    /**
     * 当 Activity 恢复可见状态时调用
     */
    override fun onResume() {
        super.onResume()
        wasResumed = true
        Logger.d(TAG, "onResume called")
        AppActivityManager.onResume()
    }

    /**
     * 当 Activity 停止时调用
     * 我们使用这个方法来检测用户是否按下了 recent 键
     */
    override fun onStop() {
        super.onStop()
        Logger.d(TAG, "onStop called")
        // 如果 Activity 之前处于恢复状态，且不是因为结束而停止，那么可能是按下了 recent 键
        if (wasResumed && !isFinishing) {
            Logger.d(TAG, "onStop called, likely pressed recent key")
            AppActivityManager.onUserLeaveHint()
        }

        wasResumed = false
    }

    override fun onDestroy() {
        Logger.d(TAG, "onDestroy called")
        super.onDestroy()
    }
}
