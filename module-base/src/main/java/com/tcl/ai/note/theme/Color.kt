package com.tcl.ai.note.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tct.theme.core.designsystem.theme.LocalTclColors
import com.tct.theme.core.designsystem.theme.TclColors

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)
val reWriteExpandBgDark = Color(0xFF383838)
val textFieldBordColor = Color(0xFF4968EE)
val lightBlue = Color(0x4D110FFF)
val yellow = Color(0xFFFFE11C)
val red = Color(0xFFFD3C31)

val ShapeColor = Color(0xFFE8EAEB)
val EraserCircleColor = Color(0xFFD8D8D8)
// 输入框光标
val CursorColor = Color(GlobalContext.instance.getColor(R.color.text_field_border))

internal val LocalTclComposeColors = staticCompositionLocalOf {
    LightColorPalette
}

object TclTheme {
    val colorScheme: TclComposeColors
        @Composable
        @ReadOnlyComposable
        get() = LocalTclComposeColors.current

    val tclColorScheme: TclColors // 公共颜色盘
        @Composable @ReadOnlyComposable get() = LocalTclColors.current

    val dimens: GlobalDimens
        @Composable @ReadOnlyComposable
        get() = LocalTclComposeDimens.current

    val textSizes: GlobalTextSizes
        @Composable @ReadOnlyComposable
        get() = LocalTclComposeTextSizes.current
}

@Immutable
class TclComposeColors(
    val tctAlertDialogBgColor: Color,
    val tctStanderTextPrimary: Color,
    val tctStanderTextSecondary: Color,
    val tctStanderTextThird: Color,
    val writingGradientLeftColor: Color,
    val writingGradientRightColor: Color,
    val reWriteGradientLeftColor: Color,
    val reWriteGradientRightColor: Color,
    val tctResultBgColor: Color,
    val reWriteExpandBg: Color,
    val tabletAudioBgColor: Color,
    val tctHighlightBgColor: Color,
    val tctTopAppBarBgColor: Color,
    val tctGlobalBgColor: Color,
    val cardSelectHint: Color,
    val primaryBackground: Color,
    val secondaryBackground: Color,
    val tertiaryBackground: Color,
    val itemSelectedBackground: Color,
    val textDialogTitle: Color,
    val textDialogItem: Color,
    val itemBackground: Color,
    val itemHoverBackground: Color,
    val lightGray: Color,
    val eraserIndicatorBackground: Color,
    val eraserIndicatorBorder: Color,
    val loadingIndicatorColor: Color,
    val textHint: Color,
    val seekBarColor: Color,
    val indicatorSelectedColor: Color,
    val indicatorUnSelectedColor: Color,
    val bottomBarColor: Color,
    val highlightColor: Color,
    val maskBackground: Color,
)

internal val LightColorPalette = TclComposeColors(
    tctAlertDialogBgColor = Color(0xFFFFFFFF),
    tctStanderTextPrimary = Color(0xFF212121),
    tctStanderTextSecondary = Color(0x7F000000),
    tctStanderTextThird = Color(0xFF3D3D3D),
    writingGradientLeftColor = Color(0xFFE4F4FF),
    writingGradientRightColor = Color(0xFFF8F6FF),
    reWriteGradientLeftColor = Color(0xFFE4F4FF),
    reWriteGradientRightColor = Color(0xFFF8F6FF),
    tctResultBgColor = Color(0xE6FFFFFF),
    reWriteExpandBg = Color(0xFFFFFFFF),
    tabletAudioBgColor = Color(0x1A989898),
    tctHighlightBgColor = Color(0xFFFFFFFF),
    tctTopAppBarBgColor = Color(0xFFF5F6F7),
    tctGlobalBgColor = Color(0xFFF5F6F7),
    cardSelectHint = Color(0xFFFFFFFF),
    primaryBackground = Color(0xFFFFFFFF),    //  适用于大部分纯白背景, 白天模式
    secondaryBackground = Color(0xFFFFFFFF),  //  适用于富文本工具栏, 白天模式
    tertiaryBackground = Color(0xFFFFFFFF),   //  适用于弹框背景, 白天模式
    itemSelectedBackground = Color(0xFFE5E5E5),
    textDialogTitle = Color(0xE6000000),
    textDialogItem = Color(0xD9000000),
    itemBackground = Color(0x0D000000),
    itemHoverBackground = Color(0x1a000000),
    lightGray = Color(0xFFCECECE),
    eraserIndicatorBackground = Color(0xFFFFFFFF),
    eraserIndicatorBorder = Color(0xFFD8D8D8),
    loadingIndicatorColor = Color(0xE6000000),
    textHint = Color(0x80000000),
    seekBarColor = Color(0xFFEBEBEB),
    indicatorSelectedColor = Color(0xFF4F4E47),
    indicatorUnSelectedColor = Color(0x404F4E47),
    bottomBarColor = Color(0xFFFFFFFF), // 底部导航栏/操作栏颜色
    highlightColor = Color(0xFFFF9E00), // 高亮色
    maskBackground = Color(0x4D000000),
)

internal val DarkColorPalette = TclComposeColors(
    tctAlertDialogBgColor = Color(0x14FFFFFF),
    tctStanderTextPrimary = Color(0xFFDEDEDE),
    tctStanderTextSecondary = Color(0x7FFFFFFF),
    tctStanderTextThird = Color(0xE5FFFFFF),
    writingGradientLeftColor = Color(0xFF1B2331),
    writingGradientRightColor = Color(0xFF251B34),
    reWriteGradientLeftColor = Color(0xFF1B2131),
    reWriteGradientRightColor = Color(0xFF231C33),
    tctResultBgColor = Color(0x14FFFFFF),
    reWriteExpandBg = reWriteExpandBgDark,
    tabletAudioBgColor = Color(0x14FFFFFF),
    tctHighlightBgColor = Color(0xFF181818),
    tctTopAppBarBgColor = Color(0xFF121212),
    tctGlobalBgColor = Color(0xFF121212),
    cardSelectHint = Color(0xFFF7F6FF),
    primaryBackground = Color(0xFF000000),   //  适用于大部分纯黑背景, 黑夜模式
    secondaryBackground = Color(0xFF1A1A1A), //  适用于富文本工具栏, 黑夜模式
    tertiaryBackground = Color(0xFF212121),  //  适用于弹框背景, 黑夜模式
    itemSelectedBackground = Color(0xFF2C2C2C),
    textDialogTitle = Color(0xFFDEDEDE),
    textDialogItem = Color(0x7FFFFFFF),
    itemBackground = Color(0x14FFFFFF),              //  适用于item背景, 黑夜模式
    itemHoverBackground = Color(0x26FFFFFF),         //  适用于item hover背景, 黑夜模式
    lightGray = Color(0x7FFFFFFF),
    eraserIndicatorBackground = Color(0xFF212121),
    eraserIndicatorBorder = Color(0x4DFFFFFF),
    loadingIndicatorColor = Color(0xE6FFFFFF),
    textHint = Color(0x80FFFFFF),
    seekBarColor = Color(0xFF1F1F1F),
    indicatorSelectedColor = Color(0xFF4F4E47),
    indicatorUnSelectedColor = Color(0x404F4E47),
    bottomBarColor = Color(0xFF252525), // 底部导航栏/操作栏颜色
    highlightColor = Color(0xFFFF9E00), // 高亮色
    maskBackground = Color(0xB3000000), // 深色模式遮罩颜色
)